/**
 * الوحدات الأساسية للتطبيق
 */

// قاعدة البيانات
const DB = {
    storageKey: 'accountants_pos_data',
    
    defaultData: {
        settings: {
            password: null, // سيتم تعيين كلمة المرور الافتراضية
            theme: 'light',
            companyInfo: {
                name: 'شركة محاسبين ولكن',
                address: 'العنوان',
                phone: '**********',
                email: '<EMAIL>',
                logo: ''
            },
            taxRate: 15,
            currency: 'ريال',
            printSettings: {
                showLogo: true,
                showCompanyInfo: true,
                paperSize: 'A4'
            }
        },
        products: [],
        customers: [
            {
                id: 1,
                name: 'ضيف',
                phone: '',
                email: '',
                address: '',
                balance: 0,
                isDefault: true,
                createdAt: new Date().toISOString()
            }
        ],
        suppliers: [],
        sales: [],
        purchases: [],
        categories: [
            { id: 1, name: 'عام', description: 'فئة عامة' }
        ],
        lastIds: {
            product: 0,
            customer: 1,
            supplier: 0,
            sale: 0,
            purchase: 0,
            category: 1
        }
    },

    init() {
        const data = this.getData();
        if (!data || Object.keys(data).length === 0) {
            this.saveData(this.defaultData);
        }
    },

    getData() {
        try {
            const data = localStorage.getItem(this.storageKey);
            return data ? JSON.parse(data) : this.defaultData;
        } catch (error) {
            console.error('خطأ في جلب البيانات:', error);
            return this.defaultData;
        }
    },

    saveData(data) {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            return false;
        }
    },

    getNextId(type) {
        const data = this.getData();
        data.lastIds[type]++;
        this.saveData(data);
        return data.lastIds[type];
    },

    backup() {
        const data = this.getData();
        const backup = {
            data: data,
            timestamp: new Date().toISOString(),
            version: '1.0.0'
        };

        const blob = new Blob([JSON.stringify(backup, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `backup_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    },

    restore(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const backup = JSON.parse(e.target.result);
                    if (backup.data) {
                        this.saveData(backup.data);
                        resolve(true);
                    } else {
                        reject('ملف النسخة الاحتياطية غير صالح');
                    }
                } catch (error) {
                    reject('خطأ في قراءة ملف النسخة الاحتياطية');
                }
            };
            reader.readAsText(file);
        });
    },

    clearAll() {
        localStorage.removeItem(this.storageKey);
        this.init();
    },

    search(table, query, fields) {
        const data = this.getData();
        const items = data[table] || [];

        if (!query || query.trim() === '') {
            return items;
        }

        const searchTerm = query.toLowerCase().trim();

        return items.filter(item => {
            return fields.some(field => {
                const value = item[field];
                if (value === null || value === undefined) return false;
                return value.toString().toLowerCase().includes(searchTerm);
            });
        });
    }
};

// نظام المصادقة
const Auth = {
    hashPassword(password) {
        let hash = 0;
        if (password.length === 0) return hash.toString();
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(16);
    },

    checkPassword(password) {
        const data = DB.getData();
        
        if (!data.settings.password) {
            return password === 'admin';
        }
        
        const hashedPassword = this.hashPassword(password);
        return data.settings.password === hashedPassword;
    },

    changePassword(oldPassword, newPassword) {
        if (this.checkPassword(oldPassword)) {
            const data = DB.getData();
            data.settings.password = this.hashPassword(newPassword);
            DB.saveData(data);
            return true;
        }
        return false;
    },

    login(password) {
        if (this.checkPassword(password)) {
            localStorage.setItem('loggedIn', 'true');
            return true;
        }
        return false;
    },

    logout() {
        localStorage.removeItem('loggedIn');
        location.reload();
    },

    isLoggedIn() {
        return localStorage.getItem('loggedIn') === 'true';
    }
};



// وظائف عامة لإغلاق النوافذ يمكن استخدامها من أي مكان
window.closeModal = function(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('hidden');
        console.log('✅ تم إغلاق النافذة:', modalId);
        return true;
    }
    return false;
};

window.closeAllModals = function() {
    const openModals = document.querySelectorAll('.modal:not(.hidden)');
    openModals.forEach(modal => {
        modal.classList.add('hidden');
        console.log('✅ تم إغلاق النافذة:', modal.id);
    });
    return openModals.length;
};

// إعادة تعريف كائن App بشكل صحيح
const App = {
    init() {
        DB.init();

        if (Auth.isLoggedIn()) {
            this.showMainApp();
        } else {
            this.showLoginForm();
        }

        this.applyTheme();
    },

    showLoginForm() {
        const loginContainer = document.getElementById('login-container');
        const mainContainer = document.getElementById('main-container');

        loginContainer.classList.remove('hidden');
        mainContainer.classList.add('hidden');

        loginContainer.innerHTML = `
            <div class="login-screen">
                <div class="login-card">
                    <div class="login-header">
                        <h1><i class="fas fa-calculator"></i> محاسبين ولكن</h1>
                        <p>نظام إدارة المبيعات</p>
                    </div>

                    <form id="login-form" class="login-form">
                        <div class="form-group">
                            <label for="password">كلمة المرور</label>
                            <div class="password-input">
                                <input type="password" id="password" class="form-control" placeholder="أدخل كلمة المرور" required>
                                <button type="button" id="toggle-password" class="password-toggle">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary login-btn">
                            <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                        </button>

                        <div class="login-help">
                            <p>كلمة المرور الافتراضية: <strong>admin</strong></p>
                        </div>
                    </form>
                </div>
            </div>
        `;

        this.attachLoginEvents();
    },

    attachLoginEvents() {
        const loginForm = document.getElementById('login-form');
        const passwordInput = document.getElementById('password');
        const togglePassword = document.getElementById('toggle-password');

        loginForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const password = passwordInput.value;

            if (Auth.login(password)) {
                this.showMainApp();
            } else {
                this.showLoginError('كلمة المرور غير صحيحة');
                passwordInput.value = '';
                passwordInput.focus();
            }
        });

        togglePassword.addEventListener('click', () => {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            const icon = togglePassword.querySelector('i');
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        });

        passwordInput.focus();
    },

    showLoginError(message) {
        const existingError = document.querySelector('.login-error');
        if (existingError) {
            existingError.remove();
        }

        const loginForm = document.getElementById('login-form');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'login-error';
        errorDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            ${message}
        `;

        loginForm.insertBefore(errorDiv, loginForm.firstChild);

        setTimeout(() => {
            errorDiv.remove();
        }, 3000);
    },

    showMainApp() {
        const loginContainer = document.getElementById('login-container');
        const mainContainer = document.getElementById('main-container');

        loginContainer.classList.add('hidden');
        mainContainer.classList.remove('hidden');

        mainContainer.innerHTML = `
            <header class="app-header">
                <div class="logo">
                    <h1>محاسبين ولكن</h1>
                </div>
                <div class="header-controls">
                    <button id="theme-toggle" class="btn">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button id="logout-btn" class="btn">
                        <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                    </button>
                </div>
            </header>

            <div class="app-container">
                <nav class="sidebar">
                    <ul class="nav-menu">
                        <li class="nav-item active" data-page="dashboard">
                            <i class="fas fa-tachometer-alt"></i> لوحة المعلومات
                        </li>
                        <li class="nav-item" data-page="products">
                            <i class="fas fa-box"></i> المنتجات
                        </li>
                        <li class="nav-item" data-page="sales">
                            <i class="fas fa-shopping-cart"></i> المبيعات
                        </li>
                        <li class="nav-item" data-page="customers">
                            <i class="fas fa-users"></i> العملاء
                        </li>
                        <li class="nav-item" data-page="suppliers">
                            <i class="fas fa-truck"></i> الموردين
                        </li>
                        <li class="nav-item" data-page="purchases">
                            <i class="fas fa-shopping-bag"></i> المشتريات
                        </li>
                        <li class="nav-item" data-page="reports">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </li>
                        <li class="nav-item" data-page="settings">
                            <i class="fas fa-cog"></i> الإعدادات
                        </li>
                    </ul>
                </nav>

                <main class="content">
                    <div id="back-button-container" class="back-button-container hidden">
                        <button id="back-button" class="btn btn-secondary back-btn">
                            <i class="fas fa-arrow-right"></i> العودة
                        </button>
                    </div>
                    <div id="page-content"></div>
                </main>
            </div>
        `;

        this.attachEvents();
        this.setupGlobalModalHandlers();
        this.setupBackButton();
        this.showPage('dashboard');
    },

    // إعداد معالجات النوافذ المنبثقة العامة
    setupGlobalModalHandlers() {
        // التأكد من عدم إضافة المعالج أكثر من مرة
        if (window.modalHandlerAdded) {
            return;
        }

        // معالج عام لجميع النوافذ المنبثقة باستخدام event delegation
        document.addEventListener('click', (e) => {
            // التحقق من جميع الطرق الممكنة لأزرار الإغلاق
            const isCloseButton =
                e.target.classList.contains('btn-close') ||
                e.target.id.includes('close-') ||
                e.target.id.includes('cancel-') ||
                e.target.textContent.trim() === '×' ||
                e.target.innerHTML.trim() === '×' ||
                e.target.getAttribute('data-dismiss') === 'modal' ||
                e.target.getAttribute('onclick')?.includes('closeModal');

            if (isCloseButton) {
                e.preventDefault();
                e.stopPropagation();

                console.log('🔴 تم النقر على زر إغلاق:', {
                    id: e.target.id,
                    className: e.target.className,
                    textContent: e.target.textContent,
                    innerHTML: e.target.innerHTML,
                    onclick: e.target.getAttribute('onclick'),
                    hasCloseClass: e.target.classList.contains('btn-close'),
                    idIncludesClose: e.target.id.includes('close-'),
                    idIncludesCancel: e.target.id.includes('cancel-')
                });

                // البحث عن النافذة المنبثقة الأقرب
                const modal = e.target.closest('.modal');
                if (modal) {
                    modal.classList.add('hidden');
                    console.log('✅ تم إغلاق النافذة:', modal.id);
                    return;
                }

                // إذا لم نجد النافذة بالطريقة العادية، ابحث عن جميع النوافذ المفتوحة
                const openModals = document.querySelectorAll('.modal:not(.hidden)');
                if (openModals.length > 0) {
                    openModals[openModals.length - 1].classList.add('hidden');
                    console.log('✅ تم إغلاق آخر نافذة مفتوحة:', openModals[openModals.length - 1].id);
                }
            }

            // إغلاق النوافذ عند النقر على الخلفية
            if (e.target.classList.contains('modal') && !e.target.classList.contains('hidden')) {
                e.target.classList.add('hidden');
                console.log('✅ تم إغلاق النافذة بالنقر على الخلفية:', e.target.id);
            }
        }, true); // استخدام capture phase

        // إغلاق النوافذ بمفتاح Escape والعودة بمفتاح Alt+Right
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModals = document.querySelectorAll('.modal:not(.hidden)');
                openModals.forEach(modal => {
                    modal.classList.add('hidden');
                    console.log('✅ تم إغلاق النافذة بمفتاح Escape:', modal.id);
                });
            }

            // اختصار العودة: Alt + السهم الأيمن
            if (e.altKey && e.key === 'ArrowRight') {
                e.preventDefault();
                this.goBack();
            }
        });

        // تعيين العلامة لمنع إضافة المعالج مرة أخرى
        window.modalHandlerAdded = true;
        console.log('🚀 تم تفعيل نظام إدارة النوافذ المنبثقة العام');
    },

    // وظيفة مساعدة لإغلاق النوافذ المنبثقة
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal && !modal.classList.contains('hidden')) {
            modal.classList.add('hidden');
            console.log('✅ تم إغلاق النافذة يدوياً:', modalId);
            return true;
        }
        return false;
    },

    // إغلاق جميع النوافذ المفتوحة
    closeAllModals() {
        const openModals = document.querySelectorAll('.modal:not(.hidden)');
        openModals.forEach(modal => {
            modal.classList.add('hidden');
            console.log('✅ تم إغلاق النافذة:', modal.id);
        });
        return openModals.length;
    },

    // إعداد زر العودة
    setupBackButton() {
        const backButton = document.getElementById('back-button');
        if (backButton) {
            backButton.addEventListener('click', () => {
                this.goBack();
            });
        }
    },

    // العودة للصفحة السابقة
    goBack() {
        if (this.navigationHistory && this.navigationHistory.length > 1) {
            // إزالة الصفحة الحالية من التاريخ
            this.navigationHistory.pop();
            // الحصول على الصفحة السابقة
            const previousPage = this.navigationHistory[this.navigationHistory.length - 1];
            // إزالة الصفحة السابقة من التاريخ لتجنب التكرار
            this.navigationHistory.pop();
            // الانتقال للصفحة السابقة
            this.showPage(previousPage);
        } else {
            // إذا لم يكن هناك تاريخ، العودة للوحة المعلومات
            this.showPage('dashboard');
        }
    },

    // إظهار زر العودة
    showBackButton() {
        const backButtonContainer = document.getElementById('back-button-container');
        if (backButtonContainer) {
            backButtonContainer.classList.remove('hidden');
        }
    },

    // إخفاء زر العودة
    hideBackButton() {
        const backButtonContainer = document.getElementById('back-button-container');
        if (backButtonContainer) {
            backButtonContainer.classList.add('hidden');
        }
    },

    attachEvents() {
        document.getElementById('theme-toggle').addEventListener('click', () => {
            this.toggleTheme();
        });

        document.getElementById('logout-btn').addEventListener('click', () => {
            Auth.logout();
        });

        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();

                navItems.forEach(i => i.classList.remove('active'));
                item.classList.add('active');

                const page = item.getAttribute('data-page');
                this.showPage(page);
            });
        });
    },

    showPage(page) {
        try {
            // تتبع تاريخ التنقل
            if (!this.navigationHistory) {
                this.navigationHistory = [];
            }

            // إضافة الصفحة الحالية للتاريخ (إذا لم تكن مكررة)
            if (this.navigationHistory[this.navigationHistory.length - 1] !== page) {
                this.navigationHistory.push(page);
            }

            // إظهار أو إخفاء زر العودة
            if (page === 'dashboard') {
                this.hideBackButton();
            } else {
                this.showBackButton();
            }

            switch(page) {
                case 'dashboard':
                    this.showDashboard();
                    break;
                case 'products':
                    if (typeof Products !== 'undefined' && Products.show) {
                        Products.show();
                    } else {
                        this.showPageNotReady('المنتجات');
                    }
                    break;
                case 'sales':
                    if (typeof Sales !== 'undefined' && Sales.show) {
                        Sales.show();
                    } else {
                        this.showPageNotReady('المبيعات');
                    }
                    break;
                case 'customers':
                    if (typeof Customers !== 'undefined' && Customers.show) {
                        Customers.show();
                    } else {
                        this.showPageNotReady('العملاء');
                    }
                    break;
                case 'suppliers':
                    if (typeof Suppliers !== 'undefined' && Suppliers.show) {
                        Suppliers.show();
                    } else {
                        this.showPageNotReady('الموردين');
                    }
                    break;
                case 'purchases':
                    if (typeof Purchases !== 'undefined' && Purchases.show) {
                        Purchases.show();
                    } else {
                        this.showPageNotReady('المشتريات');
                    }
                    break;
                case 'reports':
                    if (typeof Reports !== 'undefined' && Reports.show) {
                        Reports.show();
                    } else {
                        this.showPageNotReady('التقارير');
                    }
                    break;
                case 'settings':
                    if (typeof Settings !== 'undefined' && Settings.show) {
                        Settings.show();
                    } else {
                        this.showPageNotReady('الإعدادات');
                    }
                    break;
                default:
                    this.showPageNotReady(page);
            }
        } catch (error) {
            console.error('Error showing page:', page, error);
            this.showPageError(page, error.message);
        }
    },

    showDashboard() {
        const content = document.getElementById('page-content');
        const data = DB.getData();

        content.innerHTML = `
            <div class="page-header">
                <h2><i class="fas fa-tachometer-alt"></i> لوحة المعلومات</h2>
            </div>
            <div class="dashboard-content">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-box"></i></div>
                        <div class="stat-info">
                            <h3>المنتجات</h3>
                            <p class="stat-number">${data.products.length}</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-shopping-cart"></i></div>
                        <div class="stat-info">
                            <h3>المبيعات</h3>
                            <p class="stat-number">${data.sales.length}</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-users"></i></div>
                        <div class="stat-info">
                            <h3>العملاء</h3>
                            <p class="stat-number">${data.customers.length}</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-truck"></i></div>
                        <div class="stat-info">
                            <h3>الموردين</h3>
                            <p class="stat-number">${data.suppliers.length}</p>
                        </div>
                    </div>
                </div>

                <div class="welcome-message">
                    <h3>مرحباً بك في نظام محاسبين ولكن</h3>
                    <p>يمكنك البدء بإضافة المنتجات والعملاء من القوائم الجانبية</p>
                    <div class="quick-actions">
                        <button onclick="App.showPage('products')" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة منتج
                        </button>
                        <button onclick="App.showPage('sales')" class="btn btn-secondary">
                            <i class="fas fa-shopping-cart"></i> إنشاء فاتورة
                        </button>
                    </div>
                </div>
            </div>
        `;
    },

    showPageNotReady(pageName) {
        const content = document.getElementById('page-content');
        content.innerHTML = `
            <div class="page-header">
                <h2><i class="fas fa-cog"></i> ${pageName}</h2>
            </div>
            <div class="page-content">
                <div class="not-ready-message">
                    <i class="fas fa-tools" style="font-size: 48px; color: var(--primary-color); margin-bottom: 20px;"></i>
                    <h3>هذه الصفحة قيد التطوير</h3>
                    <p>سيتم إضافة المحتوى قريباً</p>
                    <button onclick="App.showPage('dashboard')" class="btn btn-primary">
                        <i class="fas fa-home"></i> العودة للرئيسية
                    </button>
                </div>
            </div>
        `;
    },

    showPageError(pageName, errorMessage) {
        const content = document.getElementById('page-content');
        content.innerHTML = `
            <div class="page-header">
                <h2><i class="fas fa-exclamation-triangle"></i> خطأ</h2>
            </div>
            <div class="page-content">
                <div class="error-message">
                    <h3>خطأ في تحميل صفحة ${pageName}</h3>
                    <p>تفاصيل الخطأ: ${errorMessage}</p>
                    <button onclick="App.showPage('dashboard')" class="btn btn-primary">
                        <i class="fas fa-home"></i> العودة للرئيسية
                    </button>
                </div>
            </div>
        `;
    },

    toggleTheme() {
        const body = document.body;
        const themeToggle = document.getElementById('theme-toggle');
        const data = DB.getData();

        if (body.classList.contains('dark-theme')) {
            body.classList.remove('dark-theme');
            themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
            data.settings.theme = 'light';
        } else {
            body.classList.add('dark-theme');
            themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            data.settings.theme = 'dark';
        }

        DB.saveData(data);
    },

    applyTheme() {
        const data = DB.getData();
        if (data.settings.theme === 'dark') {
            document.body.classList.add('dark-theme');
            if (document.getElementById('theme-toggle')) {
                document.getElementById('theme-toggle').innerHTML = '<i class="fas fa-sun"></i>';
            }
        }
    }
};
