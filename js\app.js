/**
 * الوظائف الرئيسية للتطبيق
 */
const App = {
    // تهيئة التطبيق
    init() {
        // تهيئة قاعدة البيانات
        DB.init();
        
        // التحقق من حالة تسجيل الدخول
        if (Auth.isLoggedIn()) {
            this.showMainApp();
        } else {
            this.showLoginForm();
        }
        
        // تطبيق الثيم المحفوظ
        this.applyTheme();
    },
    
    // عرض نموذج تسجيل الدخول
    showLoginForm() {
        const loginContainer = document.getElementById('login-container');
        const mainContainer = document.getElementById('main-container');

        loginContainer.classList.remove('hidden');
        mainContainer.classList.add('hidden');

        loginContainer.innerHTML = `
            <div class="login-screen">
                <div class="login-card">
                    <div class="login-header">
                        <h1><i class="fas fa-calculator"></i> محاسبين ولكن</h1>
                        <p>نظام إدارة المبيعات</p>
                    </div>

                    <form id="login-form" class="login-form">
                        <div class="form-group">
                            <label for="password">كلمة المرور</label>
                            <div class="password-input">
                                <input type="password" id="password" class="form-control" placeholder="أدخل كلمة المرور" required>
                                <button type="button" id="toggle-password" class="password-toggle">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary login-btn">
                            <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                        </button>

                        <div class="login-help">
                            <p>كلمة المرور الافتراضية: <strong>admin</strong></p>
                        </div>
                    </form>
                </div>
            </div>
        `;

        this.attachLoginEvents();
    },

    // إضافة أحداث شاشة تسجيل الدخول
    attachLoginEvents() {
        const loginForm = document.getElementById('login-form');
        const passwordInput = document.getElementById('password');
        const togglePassword = document.getElementById('toggle-password');

        // تسجيل الدخول
        loginForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const password = passwordInput.value;

            if (Auth.login(password)) {
                this.showMainApp();
            } else {
                this.showLoginError('كلمة المرور غير صحيحة');
                passwordInput.value = '';
                passwordInput.focus();
            }
        });

        // إظهار/إخفاء كلمة المرور
        togglePassword.addEventListener('click', () => {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            const icon = togglePassword.querySelector('i');
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        });

        // التركيز على حقل كلمة المرور
        passwordInput.focus();
    },

    // عرض خطأ تسجيل الدخول
    showLoginError(message) {
        const existingError = document.querySelector('.login-error');
        if (existingError) {
            existingError.remove();
        }

        const loginForm = document.getElementById('login-form');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'login-error';
        errorDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            ${message}
        `;

        loginForm.insertBefore(errorDiv, loginForm.firstChild);

        // إزالة الخطأ بعد 3 ثوان
        setTimeout(() => {
            errorDiv.remove();
        }, 3000);
    },
    
    // عرض التطبيق الرئيسي
    showMainApp() {
        const loginContainer = document.getElementById('login-container');
        const mainContainer = document.getElementById('main-container');
        
        loginContainer.classList.add('hidden');
        mainContainer.classList.remove('hidden');
        
        // إنشاء هيكل التطبيق الرئيسي
        mainContainer.innerHTML = `
            <header class="app-header">
                <div class="logo">
                    <h1>محاسبين ولكن</h1>
                </div>
                <div class="header-controls">
                    <button id="theme-toggle" class="btn">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button id="logout-btn" class="btn">
                        <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                    </button>
                </div>
            </header>
            
            <div class="app-container">
                <nav class="sidebar">
                    <ul class="nav-menu">
                        <li class="nav-item active" data-page="dashboard">
                            <i class="fas fa-tachometer-alt"></i> لوحة المعلومات
                        </li>
                        <li class="nav-item" data-page="products">
                            <i class="fas fa-box"></i> المنتجات
                        </li>
                        <li class="nav-item" data-page="sales">
                            <i class="fas fa-shopping-cart"></i> المبيعات
                        </li>
                        <li class="nav-item" data-page="customers">
                            <i class="fas fa-users"></i> العملاء
                        </li>
                        <li class="nav-item" data-page="suppliers">
                            <i class="fas fa-truck"></i> الموردين
                        </li>
                        <li class="nav-item" data-page="purchases">
                            <i class="fas fa-shopping-bag"></i> المشتريات
                        </li>
                        <li class="nav-item" data-page="reports">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </li>
                        <li class="nav-item" data-page="settings">
                            <i class="fas fa-cog"></i> الإعدادات
                        </li>
                    </ul>
                </nav>
                
                <main class="content">
                    <div id="page-content"></div>
                </main>
            </div>
        `;
        
        // إضافة أحداث للعناصر
        this.attachEvents();
        
        // عرض لوحة المعلومات افتراض<|im_start|>
        Dashboard.show();
    },
    
    // إضافة أحداث للعناصر
    attachEvents() {
        // حدث تبديل الثيم
        document.getElementById('theme-toggle').addEventListener('click', () => {
            this.toggleTheme();
        });
        
        // حدث تسجيل الخروج
        document.getElementById('logout-btn').addEventListener('click', () => {
            Auth.logout();
            this.showLoginForm();
        });
        
        // أحداث عناصر القائمة
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();

                // إزالة الفئة النشطة من جميع العناصر
                navItems.forEach(i => i.classList.remove('active'));

                // إضافة الفئة النشطة للعنصر المنقور
                item.classList.add('active');

                // عرض الصفحة المطلوبة
                const page = item.getAttribute('data-page');
                console.log('Navigating to page:', page); // للتشخيص
                this.showPage(page);
            });
        });
    },
    
    // عرض الصفحة المطلوبة
    showPage(page) {
        try {
            console.log('Showing page:', page); // للتشخيص

            switch(page) {
                case 'dashboard':
                    if (typeof Dashboard !== 'undefined' && Dashboard.show) {
                        Dashboard.show();
                    } else {
                        console.error('Dashboard module not found');
                    }
                    break;
                case 'products':
                    if (typeof Products !== 'undefined' && Products.show) {
                        Products.show();
                    } else {
                        console.error('Products module not found');
                    }
                    break;
                case 'sales':
                    if (typeof Sales !== 'undefined' && Sales.show) {
                        Sales.show();
                    } else {
                        console.error('Sales module not found');
                    }
                    break;
                case 'customers':
                    if (typeof Customers !== 'undefined' && Customers.show) {
                        Customers.show();
                    } else {
                        console.error('Customers module not found');
                    }
                    break;
                case 'suppliers':
                    if (typeof Suppliers !== 'undefined' && Suppliers.show) {
                        Suppliers.show();
                    } else {
                        console.error('Suppliers module not found');
                    }
                    break;
                case 'purchases':
                    if (typeof Purchases !== 'undefined' && Purchases.show) {
                        Purchases.show();
                    } else {
                        console.error('Purchases module not found');
                    }
                    break;
                case 'reports':
                    if (typeof Reports !== 'undefined' && Reports.show) {
                        Reports.show();
                    } else {
                        console.error('Reports module not found');
                    }
                    break;
                case 'settings':
                    if (typeof Settings !== 'undefined' && Settings.show) {
                        Settings.show();
                    } else {
                        console.error('Settings module not found');
                    }
                    break;
                default:
                    console.error('Unknown page:', page);
            }
        } catch (error) {
            console.error('Error showing page:', page, error);
            document.getElementById('page-content').innerHTML = `
                <div class="error-message">
                    <h3>خطأ في تحميل الصفحة</h3>
                    <p>حدث خطأ أثناء تحميل صفحة ${page}</p>
                    <p>تفاصيل الخطأ: ${error.message}</p>
                </div>
            `;
        }
    },
    
    // تبديل الثيم
    toggleTheme() {
        const body = document.body;
        const themeToggle = document.getElementById('theme-toggle');
        const data = DB.getData();
        
        if (body.classList.contains('dark-theme')) {
            body.classList.remove('dark-theme');
            themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
            data.settings.theme = 'light';
        } else {
            body.classList.add('dark-theme');
            themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            data.settings.theme = 'dark';
        }
        
        DB.saveData(data);
    },
    
    // تطبيق الثيم المحفوظ
    applyTheme() {
        const data = DB.getData();
        if (data.settings.theme === 'dark') {
            document.body.classList.add('dark-theme');
            if (document.getElementById('theme-toggle')) {
                document.getElementById('theme-toggle').innerHTML = '<i class="fas fa-sun"></i>';
            }
        }
    }
};

