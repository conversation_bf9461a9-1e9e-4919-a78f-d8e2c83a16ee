@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --border-radius: 10px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Tajawal', sans-serif;
}

body {
    background-color: var(--light-color);
    direction: rtl;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* نمط Neumorphism */
.card {
    background: var(--light-color);
    border-radius: var(--border-radius);
    box-shadow: 5px 5px 15px var(--shadow-color), 
                -5px -5px 15px rgba(255, 255, 255, 0.8);
    padding: 20px;
    margin-bottom: 20px;
    transition: var(--transition);
}

/* نمط تسجيل الدخول */
.login-form {
    max-width: 400px;
    margin: 100px auto;
    text-align: center;
}

.login-form h2 {
    margin-bottom: 20px;
    color: var(--dark-color);
}

.form-group {
    margin-bottom: 15px;
    text-align: right;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: var(--dark-color);
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: none;
    border-radius: var(--border-radius);
    background: var(--light-color);
    box-shadow: inset 2px 2px 5px var(--shadow-color), 
                inset -2px -2px 5px rgba(255, 255, 255, 0.8);
    transition: var(--transition);
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: bold;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.hidden {
    display: none;
}

/* الثيم الداكن */
.dark-theme {
    --light-color: #2c3e50;
    --dark-color: #ecf0f1;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

.dark-theme .card {
    box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.3), 
                -5px -5px 15px rgba(255, 255, 255, 0.1);
}

.dark-theme .form-control {
    box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.3),
                inset -2px -2px 5px rgba(255, 255, 255, 0.1);
    color: var(--dark-color);
}

/* هيكل التطبيق الرئيسي */
.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: var(--light-color);
    box-shadow: 0 2px 10px var(--shadow-color);
    margin-bottom: 20px;
}

.logo h1 {
    color: var(--primary-color);
    margin: 0;
    font-size: 24px;
}

.header-controls {
    display: flex;
    gap: 10px;
}

.app-container {
    display: flex;
    gap: 20px;
    min-height: calc(100vh - 120px);
}

/* الشريط الجانبي */
.sidebar {
    width: 250px;
    background: var(--light-color);
    border-radius: var(--border-radius);
    box-shadow: 5px 5px 15px var(--shadow-color),
                -5px -5px 15px rgba(255, 255, 255, 0.8);
    padding: 20px 0;
    height: fit-content;
}

.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    padding: 15px 20px;
    cursor: pointer;
    transition: var(--transition);
    border-right: 3px solid transparent;
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--dark-color);
}

.nav-item:hover {
    background: rgba(52, 152, 219, 0.1);
    border-right-color: var(--primary-color);
}

.nav-item.active {
    background: rgba(52, 152, 219, 0.2);
    border-right-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: bold;
}

.nav-item i {
    width: 20px;
    text-align: center;
}

/* المحتوى الرئيسي */
.content {
    flex: 1;
    background: var(--light-color);
    border-radius: var(--border-radius);
    box-shadow: 5px 5px 15px var(--shadow-color),
                -5px -5px 15px rgba(255, 255, 255, 0.8);
    padding: 20px;
    overflow-y: auto;
}

/* رأس الصفحة */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(52, 152, 219, 0.2);
}

.page-header h2 {
    color: var(--dark-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* الجداول */
.table-container {
    overflow-x: auto;
    margin-top: 20px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--light-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 10px var(--shadow-color);
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.data-table th {
    background: var(--primary-color);
    color: white;
    font-weight: bold;
}

.data-table tr:hover {
    background: rgba(52, 152, 219, 0.05);
}

.data-table .actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

/* الأزرار */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: bold;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-family: 'Tajawal', sans-serif;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

.btn-success {
    background: var(--secondary-color);
    color: white;
}

.btn-success:hover {
    background: #27ae60;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-info {
    background: #3498db;
    color: white;
}

.btn-info:hover {
    background: #2980b9;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

.btn-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--danger-color);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* النماذج */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--dark-color);
    font-weight: bold;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: none;
    border-radius: var(--border-radius);
    background: var(--light-color);
    box-shadow: inset 2px 2px 5px var(--shadow-color),
                inset -2px -2px 5px rgba(255, 255, 255, 0.8);
    transition: var(--transition);
    font-family: 'Tajawal', sans-serif;
    color: var(--dark-color);
}

.form-control:focus {
    outline: none;
    box-shadow: inset 2px 2px 5px var(--shadow-color),
                inset -2px -2px 5px rgba(255, 255, 255, 0.8),
                0 0 0 3px rgba(52, 152, 219, 0.2);
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--light-color);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-content.large {
    max-width: 800px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.modal-header h3 {
    margin: 0;
    color: var(--dark-color);
}

.modal form {
    padding: 20px;
}

/* لوحة المعلومات */
.dashboard {
    padding: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--light-color);
    border-radius: var(--border-radius);
    box-shadow: 5px 5px 15px var(--shadow-color),
                -5px -5px 15px rgba(255, 255, 255, 0.8);
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 8px 8px 25px var(--shadow-color),
                -8px -8px 25px rgba(255, 255, 255, 0.9);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.stat-card.sales .stat-icon {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.stat-card.products .stat-icon {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
}

.stat-card.customers .stat-icon {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.stat-card.revenue .stat-icon {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.stat-info h3 {
    margin: 0 0 5px 0;
    font-size: 28px;
    color: var(--dark-color);
    font-weight: bold;
}

.stat-info p {
    margin: 0 0 5px 0;
    color: var(--dark-color);
    font-weight: 500;
}

.stat-info small {
    color: #7f8c8d;
    font-size: 12px;
}

/* الرسوم البيانية */
.dashboard-charts {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-container {
    background: var(--light-color);
}

.chart {
    padding: 20px;
    text-align: center;
}

.top-products-list {
    padding: 20px;
}

.top-product-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.top-product-item:last-child {
    border-bottom: none;
}

.product-rank {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

.product-info {
    flex: 1;
}

.product-name {
    font-weight: bold;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.product-stats {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #7f8c8d;
}

/* التنبيهات */
.dashboard-alerts {
    margin-bottom: 30px;
}

.alerts-list {
    padding: 20px;
}

.alert-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.alert-item:last-child {
    margin-bottom: 0;
}

.alert-item.warning {
    background: rgba(243, 156, 18, 0.1);
    border-right: 4px solid var(--warning-color);
}

.alert-item.info {
    background: rgba(52, 152, 219, 0.1);
    border-right: 4px solid var(--primary-color);
}

.alert-item:hover {
    transform: translateX(-5px);
}

.alert-item i {
    font-size: 18px;
}

.alert-item.warning i {
    color: var(--warning-color);
}

.alert-item.info i {
    color: var(--primary-color);
}

.no-alerts,
.no-data {
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
    padding: 20px;
}

/* المعاملات الأخيرة */
.recent-transactions {
    margin-bottom: 30px;
}

.transaction-type {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.transaction-type.sale {
    background: rgba(46, 204, 113, 0.2);
    color: var(--secondary-color);
}

.transaction-type.purchase {
    background: rgba(231, 76, 60, 0.2);
    color: var(--danger-color);
}

/* الأرصدة */
.balance {
    font-weight: bold;
}

.balance.positive {
    color: var(--secondary-color);
}

.balance.negative {
    color: var(--danger-color);
}

/* شريط البحث */
.search-bar {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    align-items: center;
}

.search-bar .form-control {
    flex: 1;
}

/* التبويبات */
.purchases-tabs,
.sales-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 2px solid rgba(0, 0, 0, 0.1);
}

.tab-btn {
    padding: 10px 20px;
    border: none;
    background: none;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: var(--transition);
    font-family: 'Tajawal', sans-serif;
    color: var(--dark-color);
}

.tab-btn.active {
    border-bottom-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: bold;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* نموذج المبيعات/المشتريات */
.purchase-form-container,
.sales-form-container {
    max-width: 1000px;
    margin: 0 auto;
}

.purchase-header,
.sales-header {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.add-item-form {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 10px;
    align-items: end;
    margin-bottom: 20px;
    padding: 20px;
    background: rgba(52, 152, 219, 0.05);
    border-radius: var(--border-radius);
}

.items-table {
    margin-bottom: 30px;
}

.purchase-totals,
.sales-totals {
    background: rgba(52, 152, 219, 0.05);
    padding: 20px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
}

.totals-grid {
    display: grid;
    gap: 10px;
    max-width: 300px;
    margin-left: auto;
}

.total-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
}

.total-item.grand-total {
    border-top: 2px solid var(--primary-color);
    padding-top: 10px;
    margin-top: 10px;
    font-weight: bold;
    font-size: 18px;
}

.purchase-actions,
.sales-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* معلومات التاريخ */
.date-info {
    color: #7f8c8d;
    font-size: 14px;
}

/* حالات الفواتير */
.status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.status.completed {
    background: rgba(46, 204, 113, 0.2);
    color: var(--secondary-color);
}

.status.pending {
    background: rgba(243, 156, 18, 0.2);
    color: var(--warning-color);
}

.status.cancelled {
    background: rgba(231, 76, 60, 0.2);
    color: var(--danger-color);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        order: 2;
    }

    .content {
        order: 1;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-charts {
        grid-template-columns: 1fr;
    }

    .add-item-form {
        grid-template-columns: 1fr;
    }

    .purchase-header,
    .sales-header {
        grid-template-columns: 1fr;
    }

    .search-bar {
        flex-direction: column;
    }

    .page-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
}

/* تحسينات إضافية */
.text-center {
    text-align: center;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-item label {
    font-weight: bold;
    color: #7f8c8d;
    font-size: 14px;
}

.info-item span {
    color: var(--dark-color);
    font-size: 16px;
}

.customer-info,
.supplier-info {
    margin-bottom: 30px;
}

.customer-transactions,
.supplier-transactions {
    margin-bottom: 30px;
}

.transactions-list {
    max-height: 200px;
    overflow-y: auto;
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.transaction-item:last-child {
    border-bottom: none;
}

.customer-actions,
.supplier-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.amount {
    font-weight: bold;
    color: var(--primary-color);
}

/* حالات المخزون */
.stock-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.stock-status.good {
    background: rgba(46, 204, 113, 0.2);
    color: var(--secondary-color);
}

.stock-status.low {
    background: rgba(243, 156, 18, 0.2);
    color: var(--warning-color);
}

.stock-status.out {
    background: rgba(231, 76, 60, 0.2);
    color: var(--danger-color);
}

/* عناصر الفئات */
.category-item {
    margin-bottom: 10px;
}

.category-item:last-child {
    margin-bottom: 0;
}

/* تحسينات إضافية للثيم الداكن */
.dark-theme .stat-card {
    box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.3),
                -5px -5px 15px rgba(255, 255, 255, 0.1);
}

.dark-theme .data-table {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.dark-theme .data-table tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

.dark-theme .alert-item.warning {
    background: rgba(243, 156, 18, 0.2);
}

.dark-theme .alert-item.info {
    background: rgba(52, 152, 219, 0.2);
}

.dark-theme .purchase-totals,
.dark-theme .sales-totals {
    background: rgba(52, 152, 219, 0.1);
}

.dark-theme .add-item-form {
    background: rgba(52, 152, 219, 0.1);
}

/* تحسينات للطباعة */
@media print {
    .sidebar,
    .app-header,
    .page-header button,
    .actions,
    .btn {
        display: none !important;
    }

    .app-container {
        flex-direction: column;
    }

    .content {
        box-shadow: none;
        padding: 0;
    }

    .data-table {
        width: 100%;
        font-size: 12px;
    }

    .data-table th,
    .data-table td {
        padding: 8px 4px;
        border: 1px solid #000;
    }

    .card {
        box-shadow: none;
        border: 1px solid #000;
    }
}

/* تحسينات للأجهزة اللوحية */
@media (max-width: 1024px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .dashboard-charts {
        grid-template-columns: 1fr;
    }

    .sidebar {
        width: 200px;
    }

    .nav-item {
        padding: 12px 15px;
        font-size: 14px;
    }

    .add-item-form {
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .page-header {
        padding: 10px 0;
    }

    .page-header h2 {
        font-size: 18px;
    }

    .btn {
        padding: 8px 12px;
        font-size: 12px;
    }

    .data-table {
        font-size: 12px;
    }

    .data-table th,
    .data-table td {
        padding: 8px 4px;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .form-actions {
        flex-direction: column;
        gap: 10px;
    }

    .form-actions .btn {
        width: 100%;
    }
}

/* تحسينات لإمكانية الوصول */
.btn:focus,
.form-control:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.nav-item:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: -2px;
}

/* تحسينات للحركة */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* تحسينات للتباين العالي */
@media (prefers-contrast: high) {
    :root {
        --shadow-color: rgba(0, 0, 0, 0.5);
    }

    .card {
        border: 2px solid var(--dark-color);
    }

    .btn {
        border: 2px solid currentColor;
    }
}

/* أنماط إضافية للأرقام العربية */
.arabic-numbers {
    font-feature-settings: "lnum" 0;
    font-variant-numeric: normal;
}

/* تحسينات للنصوص الطويلة */
.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.text-wrap {
    word-wrap: break-word;
    word-break: break-word;
}

/* أنماط للرسائل */
.message {
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    border-right: 4px solid;
}

.message.success {
    background: rgba(46, 204, 113, 0.1);
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

.message.error {
    background: rgba(231, 76, 60, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.message.warning {
    background: rgba(243, 156, 18, 0.1);
    border-color: var(--warning-color);
    color: var(--warning-color);
}

.message.info {
    background: rgba(52, 152, 219, 0.1);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* طرق الدفع */
.payment-method {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.payment-method.cash {
    background: rgba(46, 204, 113, 0.2);
    color: var(--secondary-color);
}

.payment-method.credit {
    background: rgba(243, 156, 18, 0.2);
    color: var(--warning-color);
}

/* تحسينات إضافية للفواتير */
.sale-header-info,
.sale-items-info,
.sale-totals-info {
    margin-bottom: 30px;
}

.sale-header-info h4,
.sale-items-info h4,
.sale-totals-info h4 {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
}

/* تحسينات للطباعة - فواتير */
@media print {
    .sale-actions,
    .modal-header {
        display: none !important;
    }

    .sale-header-info h4,
    .sale-items-info h4 {
        color: #000 !important;
        border-bottom: 2px solid #000 !important;
    }

    .info-grid {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 10px !important;
    }

    .totals-grid {
        max-width: none !important;
        margin: 0 !important;
    }
}

/* تحسينات للأرقام العربية الهندية */
.arabic-numerals {
    font-feature-settings: "lnum" 0;
}

/* تحسينات للتمرير */
.table-container {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) var(--light-color);
}

.table-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.table-container::-webkit-scrollbar-track {
    background: var(--light-color);
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: #2980b9;
}

/* تحسينات للتركيز */
.form-control:focus-visible,
.btn:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* تحسينات للحالة المعطلة */
.form-control:disabled,
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

/* تحسينات للتحديد */
::selection {
    background: rgba(52, 152, 219, 0.3);
    color: var(--dark-color);
}

/* تحسينات للروابط */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: #2980b9;
    text-decoration: underline;
}

/* تحسينات للقوائم */
ul, ol {
    padding-right: 20px;
}

li {
    margin-bottom: 5px;
}

/* تحسينات للعناوين */
h1, h2, h3, h4, h5, h6 {
    color: var(--dark-color);
    margin-bottom: 15px;
}

h1 { font-size: 2.5em; }
h2 { font-size: 2em; }
h3 { font-size: 1.75em; }
h4 { font-size: 1.5em; }
h5 { font-size: 1.25em; }
h6 { font-size: 1em; }

/* تحسينات للفقرات */
p {
    line-height: 1.6;
    margin-bottom: 15px;
}

/* تحسينات للكود */
code {
    background: rgba(0, 0, 0, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

pre {
    background: rgba(0, 0, 0, 0.05);
    padding: 15px;
    border-radius: var(--border-radius);
    overflow-x: auto;
    border-right: 4px solid var(--primary-color);
}

pre code {
    background: none;
    padding: 0;
}

/* تحسينات للاقتباسات */
blockquote {
    border-right: 4px solid var(--primary-color);
    padding: 15px 20px;
    margin: 20px 0;
    background: rgba(52, 152, 219, 0.05);
    font-style: italic;
}

/* تحسينات للخطوط الفاصلة */
hr {
    border: none;
    height: 2px;
    background: linear-gradient(to left, transparent, var(--primary-color), transparent);
    margin: 30px 0;
}

/* تحسينات للشارات */
.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: bold;
    border-radius: 12px;
    text-align: center;
    white-space: nowrap;
}

.badge.primary {
    background: var(--primary-color);
    color: white;
}

.badge.secondary {
    background: var(--secondary-color);
    color: white;
}

.badge.warning {
    background: var(--warning-color);
    color: white;
}

.badge.danger {
    background: var(--danger-color);
    color: white;
}

/* تحسينات للتقدم */
.progress {
    width: 100%;
    height: 20px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.3s ease;
    border-radius: 10px;
}

/* تحسينات للتبديل */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* تحسينات للتحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(52, 152, 219, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تحسينات للتنبيهات المنبثقة */
.toast {
    position: fixed;
    top: 20px;
    left: 20px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 15px 20px;
    z-index: 1001;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-right: 4px solid var(--secondary-color);
}

.toast.error {
    border-right: 4px solid var(--danger-color);
}

.toast.warning {
    border-right: 4px solid var(--warning-color);
}

.toast.info {
    border-right: 4px solid var(--primary-color);
}

/* أنماط التقارير */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.report-card {
    padding: 25px;
}

.report-card h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.report-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.report-buttons .btn {
    justify-content: flex-start;
    text-align: right;
}

.custom-report-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.custom-report-form .form-group {
    margin-bottom: 0;
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 2px solid var(--primary-color);
    margin-bottom: 20px;
}

.report-header h3 {
    margin: 0;
    color: var(--primary-color);
}

.report-actions {
    display: flex;
    gap: 10px;
}

.report-summary {
    margin-bottom: 30px;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-item {
    background: var(--light-color);
    border-radius: var(--border-radius);
    box-shadow: 2px 2px 10px var(--shadow-color),
                -2px -2px 10px rgba(255, 255, 255, 0.8);
    padding: 20px;
    text-align: center;
}

.summary-item h4 {
    margin: 0 0 10px 0;
    color: var(--dark-color);
    font-size: 14px;
}

.summary-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
    display: block;
}

.report-table {
    overflow-x: auto;
}

.report-table .data-table {
    margin-bottom: 0;
}

/* كشف الحساب */
.customer-statement {
    padding: 20px;
}

.customer-info {
    background: rgba(52, 152, 219, 0.1);
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
}

.customer-info p {
    margin: 5px 0;
}

.transactions-table {
    margin-top: 20px;
}

.debit {
    color: var(--danger-color);
    font-weight: bold;
}

.credit {
    color: var(--secondary-color);
    font-weight: bold;
}

/* تحسينات للطباعة - التقارير */
@media print {
    .reports-grid,
    .report-actions,
    .page-header {
        display: none !important;
    }

    .report-header {
        border-bottom: 2px solid #000 !important;
        page-break-after: avoid;
    }

    .report-header h3 {
        color: #000 !important;
    }

    .summary-grid {
        display: grid !important;
        grid-template-columns: repeat(4, 1fr) !important;
        gap: 10px !important;
        margin-bottom: 20px !important;
    }

    .summary-item {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        padding: 10px !important;
    }

    .summary-value {
        color: #000 !important;
    }

    .report-table {
        page-break-inside: avoid;
    }

    .data-table {
        font-size: 12px !important;
    }

    .data-table th,
    .data-table td {
        padding: 6px 4px !important;
        border: 1px solid #000 !important;
    }

    .data-table th {
        background-color: #f0f0f0 !important;
        color: #000 !important;
    }

    .stock-status.good {
        color: #000 !important;
    }

    .stock-status.low {
        color: #000 !important;
        background: #f0f0f0 !important;
    }

    .stock-status.out {
        color: #000 !important;
        background: #e0e0e0 !important;
    }

    .balance.positive {
        color: #000 !important;
    }

    .balance.negative {
        color: #000 !important;
        font-weight: bold !important;
    }

    .debit {
        color: #000 !important;
    }

    .credit {
        color: #000 !important;
    }
}

/* تحسينات للشاشات الصغيرة - التقارير */
@media (max-width: 768px) {
    .reports-grid {
        grid-template-columns: 1fr;
    }

    .report-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .report-actions {
        flex-wrap: wrap;
        justify-content: center;
    }

    .summary-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .report-buttons .btn {
        font-size: 14px;
        padding: 10px 15px;
    }
}

@media (max-width: 480px) {
    .summary-grid {
        grid-template-columns: 1fr;
    }

    .report-actions {
        flex-direction: column;
        width: 100%;
    }

    .report-actions .btn {
        width: 100%;
    }

    .custom-report-form .btn {
        width: 100%;
    }
}

/* تحسينات إضافية للتقارير */
.report-card:hover {
    transform: translateY(-2px);
    box-shadow: 8px 8px 25px var(--shadow-color),
                -8px -8px 25px rgba(255, 255, 255, 0.9);
}

.summary-item:hover {
    transform: translateY(-2px);
    box-shadow: 4px 4px 15px var(--shadow-color),
                -4px -4px 15px rgba(255, 255, 255, 0.9);
}

/* أنماط للرسوم البيانية البسيطة */
.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

.chart-container canvas {
    max-width: 100%;
    height: auto;
}

/* أنماط للإحصائيات السريعة */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.quick-stat {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 15px;
    border-radius: var(--border-radius);
    text-align: center;
}

.quick-stat h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    opacity: 0.9;
}

.quick-stat .value {
    font-size: 20px;
    font-weight: bold;
}

/* أنماط للمقارنات */
.comparison-table {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.comparison-item {
    background: var(--light-color);
    border-radius: var(--border-radius);
    box-shadow: 2px 2px 10px var(--shadow-color);
    padding: 20px;
    text-align: center;
}

.comparison-item h5 {
    margin: 0 0 10px 0;
    color: var(--dark-color);
}

.comparison-value {
    font-size: 18px;
    font-weight: bold;
    color: var(--primary-color);
}

.comparison-change {
    font-size: 12px;
    margin-top: 5px;
}

.comparison-change.positive {
    color: var(--secondary-color);
}

.comparison-change.negative {
    color: var(--danger-color);
}

/* أنماط للتصفية المتقدمة */
.advanced-filters {
    background: rgba(52, 152, 219, 0.05);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.filter-row:last-child {
    margin-bottom: 0;
}

/* أنماط للتصدير */
.export-options {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.export-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-family: 'Tajawal', sans-serif;
}

.export-btn.excel {
    background: #1d6f42;
    color: white;
}

.export-btn.pdf {
    background: #dc3545;
    color: white;
}

.export-btn.csv {
    background: #6c757d;
    color: white;
}

.export-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* أنماط الإعدادات */
.settings-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.settings-section {
    padding: 25px;
}

.settings-section h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.logo-preview {
    margin-top: 10px;
}

.logo-preview img {
    max-width: 200px;
    max-height: 100px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    padding: 5px;
}

.data-management {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.data-size,
.last-backup {
    font-weight: bold;
    color: var(--primary-color);
    padding: 10px;
    background: rgba(52, 152, 219, 0.1);
    border-radius: var(--border-radius);
}

.data-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* رسائل الخطأ */
.error-message {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid var(--danger-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    margin: 20px;
}

.error-message h3 {
    color: var(--danger-color);
    margin-bottom: 10px;
}

.error-message p {
    color: var(--dark-color);
    margin-bottom: 10px;
}

/* تحسينات للشاشات الصغيرة - الإعدادات */
@media (max-width: 768px) {
    .settings-container {
        grid-template-columns: 1fr;
    }

    .data-actions {
        flex-direction: column;
    }

    .data-actions .btn {
        width: 100%;
    }
}

/* تحسينات للطباعة - الإعدادات */
@media print {
    .settings-container,
    .data-actions,
    .form-actions {
        display: none !important;
    }
}

/* أنماط إضافية للتفاعل */
.nav-item {
    cursor: pointer;
    user-select: none;
}

.nav-item:hover {
    background: rgba(52, 152, 219, 0.1);
}

.nav-item.active {
    background: rgba(52, 152, 219, 0.2);
    color: var(--primary-color);
    font-weight: bold;
}

/* تحسينات للتحميل */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(52, 152, 219, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تحسينات للتركيز */
.nav-item:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* تحسينات للحالة المعطلة */
.nav-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* أنماط للإشعارات */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 15px 20px;
    z-index: 1001;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 300px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-right: 4px solid var(--secondary-color);
}

.notification.error {
    border-right: 4px solid var(--danger-color);
}

.notification.warning {
    border-right: 4px solid var(--warning-color);
}

.notification.info {
    border-right: 4px solid var(--primary-color);
}

.notification .close-btn {
    position: absolute;
    top: 5px;
    left: 5px;
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #999;
}

.notification .close-btn:hover {
    color: #333;
}

/* تحسينات للوصولية */
@media (prefers-reduced-motion: reduce) {
    .notification {
        transition: none;
    }

    .loading-spinner {
        animation: none;
    }
}

/* تحسينات للتباين العالي */
@media (prefers-contrast: high) {
    .nav-item.active {
        background: #000;
        color: #fff;
    }

    .error-message {
        border-width: 3px;
    }
}

/* أنماط للطباعة المحسنة */
@media print {
    .notification,
    .loading-spinner,
    .nav-item {
        display: none !important;
    }

    .error-message {
        border: 2px solid #000 !important;
        background: #f0f0f0 !important;
    }
}

/* أنماط شاشة تسجيل الدخول */
.login-screen {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    padding: 20px;
}

.login-card {
    background: var(--light-color);
    border-radius: 20px;
    box-shadow: 20px 20px 60px var(--shadow-color),
                -20px -20px 60px rgba(255, 255, 255, 0.8);
    padding: 40px;
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-header h1 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.login-header p {
    color: var(--dark-color);
    margin-bottom: 30px;
    opacity: 0.8;
}

.login-form {
    text-align: right;
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--dark-color);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: var(--transition);
}

.password-toggle:hover {
    background: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
}

.login-btn {
    width: 100%;
    padding: 15px;
    font-size: 16px;
    font-weight: bold;
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.login-help {
    margin-top: 20px;
    padding: 15px;
    background: rgba(52, 152, 219, 0.1);
    border-radius: var(--border-radius);
    font-size: 14px;
}

.login-help strong {
    color: var(--primary-color);
    font-family: monospace;
    background: rgba(52, 152, 219, 0.2);
    padding: 2px 6px;
    border-radius: 4px;
}

.login-error {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid var(--danger-color);
    border-radius: var(--border-radius);
    padding: 10px;
    margin-bottom: 15px;
    color: var(--danger-color);
    display: flex;
    align-items: center;
    gap: 8px;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* تحسينات للشاشات الصغيرة - تسجيل الدخول */
@media (max-width: 480px) {
    .login-card {
        padding: 30px 20px;
        margin: 10px;
    }

    .login-header h1 {
        font-size: 24px;
    }
}

/* المظهر الداكن - تسجيل الدخول */
.dark-theme .login-screen {
    background: linear-gradient(135deg, #2c3e50, #34495e);
}

.dark-theme .login-card {
    background: var(--dark-color);
    box-shadow: 20px 20px 60px rgba(0, 0, 0, 0.5),
                -20px -20px 60px rgba(255, 255, 255, 0.1);
}

.dark-theme .login-header h1 {
    color: var(--primary-color);
}

.dark-theme .login-header p {
    color: var(--light-color);
}

.dark-theme .password-toggle {
    color: var(--light-color);
}

.dark-theme .password-toggle:hover {
    background: rgba(52, 152, 219, 0.2);
    color: var(--primary-color);
}

.dark-theme .login-help {
    background: rgba(52, 152, 219, 0.2);
    color: var(--light-color);
}

.dark-theme .login-error {
    background: rgba(231, 76, 60, 0.2);
    color: #ff6b6b;
}

/* أنماط لوحة المعلومات */
.dashboard-content {
    padding: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--light-color);
    border-radius: var(--border-radius);
    box-shadow: 8px 8px 20px var(--shadow-color),
                -8px -8px 20px rgba(255, 255, 255, 0.8);
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 12px 12px 30px var(--shadow-color),
                -12px -12px 30px rgba(255, 255, 255, 0.9);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.stat-info h3 {
    margin: 0 0 5px 0;
    color: var(--dark-color);
    font-size: 16px;
}

.stat-number {
    margin: 0;
    font-size: 28px;
    font-weight: bold;
    color: var(--primary-color);
}

.welcome-message {
    background: rgba(52, 152, 219, 0.1);
    border-radius: var(--border-radius);
    padding: 30px;
    text-align: center;
    box-shadow: inset 4px 4px 10px var(--shadow-color),
                inset -4px -4px 10px rgba(255, 255, 255, 0.8);
}

.welcome-message h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 24px;
}

.welcome-message p {
    color: var(--dark-color);
    font-size: 16px;
    opacity: 0.8;
    margin-bottom: 20px;
}

.quick-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.quick-actions .btn {
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.quick-actions .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

/* أنماط للصفحات غير الجاهزة */
.not-ready-message {
    text-align: center;
    padding: 60px 20px;
    color: var(--dark-color);
}

.not-ready-message h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 24px;
}

.not-ready-message p {
    font-size: 16px;
    margin-bottom: 30px;
    opacity: 0.8;
}

/* أنماط الصفحات العامة */
.page-content {
    padding: 20px;
    text-align: center;
}

.page-content p {
    font-size: 18px;
    color: var(--dark-color);
    margin: 10px 0;
}

/* المظهر الداكن - لوحة المعلومات */
.dark-theme .stat-card {
    background: var(--dark-color);
    box-shadow: 8px 8px 20px rgba(0, 0, 0, 0.3),
                -8px -8px 20px rgba(255, 255, 255, 0.1);
}

.dark-theme .stat-card:hover {
    box-shadow: 12px 12px 30px rgba(0, 0, 0, 0.4),
                -12px -12px 30px rgba(255, 255, 255, 0.15);
}

.dark-theme .stat-info h3 {
    color: var(--light-color);
}

.dark-theme .welcome-message {
    background: rgba(52, 152, 219, 0.2);
    box-shadow: inset 4px 4px 10px rgba(0, 0, 0, 0.3),
                inset -4px -4px 10px rgba(255, 255, 255, 0.1);
}

.dark-theme .welcome-message p {
    color: var(--light-color);
}

.dark-theme .page-content p {
    color: var(--light-color);
}

/* تحسينات للشاشات الصغيرة - لوحة المعلومات */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .stat-card {
        padding: 20px 15px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .stat-number {
        font-size: 24px;
    }

    .welcome-message {
        padding: 20px;
    }

    .welcome-message h3 {
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-content {
        padding: 15px;
    }
}

/* أنماط نموذج المنتجات المحسن */
.product-form-modal {
    max-width: 1200px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
}

.product-form-grid {
    padding: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 20px;
    align-items: end;
}

.form-row.full-width {
    grid-template-columns: 1fr;
}

.form-row .form-group {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-color);
    font-size: 14px;
}

.form-group .form-control {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
    background: var(--light-color);
}

.form-group .form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-group .form-control:required {
    border-left: 4px solid var(--primary-color);
}

.form-group textarea.form-control {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    padding-top: 20px;
    border-top: 2px solid #e1e5e9;
    margin-top: 30px;
}

.form-actions .btn {
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 140px;
    justify-content: center;
}

.form-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* أنماط خاصة للحقول */
.form-group input[type="number"] {
    text-align: center;
}

.form-group select {
    cursor: pointer;
}

.form-group input:invalid {
    border-color: var(--danger-color);
}

.form-group input:valid {
    border-color: var(--secondary-color);
}

/* تحسينات للشاشات الصغيرة - نموذج المنتجات */
@media (max-width: 1024px) {
    .product-form-modal {
        max-width: 95%;
        margin: 20px auto;
    }

    .form-row {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .product-form-modal {
        max-width: 100%;
        margin: 10px;
        max-height: 95vh;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
        margin-bottom: 15px;
    }

    .product-form-grid {
        padding: 15px;
    }

    .form-actions {
        flex-direction: column;
        gap: 10px;
    }

    .form-actions .btn {
        width: 100%;
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .form-group .form-control {
        padding: 10px;
        font-size: 16px; /* منع التكبير في iOS */
    }

    .form-group label {
        font-size: 13px;
    }
}

/* المظهر الداكن - نموذج المنتجات */
.dark-theme .form-group .form-control {
    background: var(--dark-color);
    border-color: #4a5568;
    color: var(--light-color);
}

.dark-theme .form-group .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.dark-theme .form-group label {
    color: var(--light-color);
}

.dark-theme .form-actions {
    border-top-color: #4a5568;
}

/* أنماط للتحقق من صحة البيانات */
.form-group .error-message {
    color: var(--danger-color);
    font-size: 12px;
    margin-top: 5px;
    display: none;
}

.form-group.has-error .form-control {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.form-group.has-error .error-message {
    display: block;
}

.form-group.has-success .form-control {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(46, 204, 113, 0.1);
}

/* أنماط للحقول المطلوبة */
.form-group label.required::after {
    content: " *";
    color: var(--danger-color);
    font-weight: bold;
}

/* أنماط للتلميحات */
.form-group .form-hint {
    font-size: 11px;
    color: #6c757d;
    margin-top: 3px;
    font-style: italic;
}

/* تحسينات للطباعة - نموذج المنتجات */
@media print {
    .product-form-modal,
    .modal {
        display: none !important;
    }
}

/* أنماط نافذة تفاصيل المنتج */
.product-details {
    padding: 20px;
}

.product-details h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    text-align: center;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 30px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item label {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 14px;
}

.detail-item span {
    padding: 8px 12px;
    background: rgba(52, 152, 219, 0.1);
    border-radius: var(--border-radius);
    border: 1px solid rgba(52, 152, 219, 0.2);
    font-size: 14px;
}

.details-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    padding-top: 20px;
    border-top: 2px solid #e1e5e9;
}

.details-actions .btn {
    padding: 10px 20px;
    font-size: 14px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    gap: 8px;
}

/* أنماط حالة المخزون المحسنة */
.stock-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
    display: inline-block;
    min-width: 80px;
}

.stock-status.good {
    background: rgba(46, 204, 113, 0.2);
    color: #27ae60;
    border: 1px solid #27ae60;
}

.stock-status.low {
    background: rgba(241, 196, 15, 0.2);
    color: #f39c12;
    border: 1px solid #f39c12;
}

.stock-status.reorder {
    background: rgba(230, 126, 34, 0.2);
    color: #e67e22;
    border: 1px solid #e67e22;
}

.stock-status.out {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    border: 1px solid #e74c3c;
}

/* تحسينات للشاشات الصغيرة - تفاصيل المنتج */
@media (max-width: 768px) {
    .details-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .details-actions {
        flex-direction: column;
        gap: 10px;
    }

    .details-actions .btn {
        width: 100%;
        justify-content: center;
    }
}

/* المظهر الداكن - تفاصيل المنتج */
.dark-theme .product-details h3 {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.dark-theme .detail-item label {
    color: var(--light-color);
}

.dark-theme .detail-item span {
    background: rgba(52, 152, 219, 0.2);
    border-color: rgba(52, 152, 219, 0.3);
    color: var(--light-color);
}

.dark-theme .details-actions {
    border-top-color: #4a5568;
}

/* أنماط الجدول المحسنة */
.data-table {
    font-size: 13px;
}

.data-table th {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-weight: 600;
    text-align: center;
    padding: 12px 8px;
    font-size: 12px;
}

.data-table td {
    padding: 10px 8px;
    text-align: center;
    vertical-align: middle;
    border-bottom: 1px solid #e1e5e9;
}

.data-table .actions {
    white-space: nowrap;
}

.data-table .actions .btn {
    padding: 6px 8px;
    margin: 0 2px;
    font-size: 12px;
}

/* تحسينات للطباعة - الجدول */
@media print {
    .data-table {
        font-size: 10px;
    }

    .data-table .actions {
        display: none;
    }

    .stock-status {
        color: #000 !important;
        background: #f0f0f0 !important;
        border: 1px solid #000 !important;
    }
}


