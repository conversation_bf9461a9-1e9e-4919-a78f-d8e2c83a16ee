/**
 * نظام اليوميات المحاسبية
 */
const Journals = {
    currentJournal: {
        entries: [],
        totalDebit: 0,
        totalCredit: 0
    },

    // عرض صفحة اليوميات
    show() {
        const content = document.getElementById('page-content');
        content.innerHTML = `
            <div class="page-header">
                <h2><i class="fas fa-book"></i> اليوميات المحاسبية</h2>
                <div>
                    <button id="new-journal-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إدراج يومية جديد
                    </button>
                    <button id="journal-reports-btn" class="btn btn-info">
                        <i class="fas fa-chart-line"></i> تقارير اليوميات
                    </button>
                </div>
            </div>

            <div class="journals-tabs">
                <button class="tab-btn active" data-tab="list">قائمة اليوميات</button>
                <button class="tab-btn" data-tab="daily-expenses">مصروفات يومية</button>
                <button class="tab-btn" data-tab="daily-income">إيرادات يومية</button>
                <button class="tab-btn" data-tab="customer-settlements">تسويات عملاء</button>
                <button class="tab-btn" data-tab="general-settlements">تسويات عامة</button>
                <button class="tab-btn" data-tab="new-entry">إدراج يومية</button>
            </div>

            <!-- قائمة اليوميات -->
            <div id="list-tab" class="tab-content active">
                <div class="card">
                    <div class="search-bar">
                        <input type="text" id="journal-search" class="form-control" placeholder="البحث في اليوميات...">
                        <select id="journal-type-filter" class="form-control">
                            <option value="">جميع الأنواع</option>
                            <option value="expense">مصروفات</option>
                            <option value="income">إيرادات</option>
                            <option value="customer_settlement">تسوية عملاء</option>
                            <option value="general_settlement">تسوية عامة</option>
                            <option value="manual">إدراج يدوي</option>
                        </select>
                        <input type="date" id="journal-date-filter" class="form-control">
                    </div>

                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم الإدراج</th>
                                    <th>التاريخ</th>
                                    <th>النوع</th>
                                    <th>الوصف</th>
                                    <th>المدين</th>
                                    <th>الدائن</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="journals-table-body">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- مصروفات يومية -->
            <div id="daily-expenses-tab" class="tab-content">
                <div class="card">
                    <h3>المصروفات اليومية</h3>
                    <form id="daily-expenses-form" class="journal-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="expense-date">التاريخ *</label>
                                <input type="date" id="expense-date" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="expense-amount">المبلغ *</label>
                                <input type="number" id="expense-amount" class="form-control" step="0.01" min="0" required>
                            </div>
                            <div class="form-group">
                                <label for="expense-category">فئة المصروف *</label>
                                <select id="expense-category" class="form-control" required>
                                    <option value="">اختر الفئة</option>
                                    <option value="rent">إيجار</option>
                                    <option value="utilities">مرافق</option>
                                    <option value="salaries">رواتب</option>
                                    <option value="supplies">مستلزمات</option>
                                    <option value="maintenance">صيانة</option>
                                    <option value="transportation">مواصلات</option>
                                    <option value="marketing">تسويق</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="expense-description">وصف المصروف *</label>
                            <input type="text" id="expense-description" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="expense-notes">ملاحظات</label>
                            <textarea id="expense-notes" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ المصروف
                            </button>
                            <button type="button" onclick="Journals.clearExpenseForm()" class="btn btn-secondary">
                                <i class="fas fa-eraser"></i> مسح
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- إيرادات يومية -->
            <div id="daily-income-tab" class="tab-content">
                <div class="card">
                    <h3>الإيرادات اليومية</h3>
                    <form id="daily-income-form" class="journal-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="income-date">التاريخ *</label>
                                <input type="date" id="income-date" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="income-amount">المبلغ *</label>
                                <input type="number" id="income-amount" class="form-control" step="0.01" min="0" required>
                            </div>
                            <div class="form-group">
                                <label for="income-source">مصدر الإيراد *</label>
                                <select id="income-source" class="form-control" required>
                                    <option value="">اختر المصدر</option>
                                    <option value="sales">مبيعات</option>
                                    <option value="services">خدمات</option>
                                    <option value="investment">استثمارات</option>
                                    <option value="rent_income">إيراد إيجار</option>
                                    <option value="commission">عمولات</option>
                                    <option value="interest">فوائد</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="income-description">وصف الإيراد *</label>
                            <input type="text" id="income-description" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="income-notes">ملاحظات</label>
                            <textarea id="income-notes" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الإيراد
                            </button>
                            <button type="button" onclick="Journals.clearIncomeForm()" class="btn btn-secondary">
                                <i class="fas fa-eraser"></i> مسح
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- تسويات عملاء يومية -->
            <div id="customer-settlements-tab" class="tab-content">
                <div class="card">
                    <h3>تسويات العملاء اليومية</h3>
                    <form id="customer-settlements-form" class="journal-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="settlement-date">التاريخ *</label>
                                <input type="date" id="settlement-date" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="settlement-customer">العميل *</label>
                                <select id="settlement-customer" class="form-control" required>
                                    <option value="">اختر العميل</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="settlement-type">نوع التسوية *</label>
                                <select id="settlement-type" class="form-control" required>
                                    <option value="">اختر النوع</option>
                                    <option value="payment">دفعة من العميل</option>
                                    <option value="refund">مرتجع للعميل</option>
                                    <option value="discount">خصم</option>
                                    <option value="adjustment">تسوية رصيد</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="settlement-amount">المبلغ *</label>
                                <input type="number" id="settlement-amount" class="form-control" step="0.01" min="0" required>
                            </div>
                            <div class="form-group">
                                <label for="settlement-method">طريقة الدفع</label>
                                <select id="settlement-method" class="form-control">
                                    <option value="cash">نقد</option>
                                    <option value="bank">تحويل بنكي</option>
                                    <option value="check">شيك</option>
                                    <option value="card">بطاقة</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="settlement-reference">رقم المرجع</label>
                                <input type="text" id="settlement-reference" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="settlement-description">وصف التسوية *</label>
                            <input type="text" id="settlement-description" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="settlement-notes">ملاحظات</label>
                            <textarea id="settlement-notes" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التسوية
                            </button>
                            <button type="button" onclick="Journals.clearSettlementForm()" class="btn btn-secondary">
                                <i class="fas fa-eraser"></i> مسح
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- تسويات عامة -->
            <div id="general-settlements-tab" class="tab-content">
                <div class="card">
                    <h3>التسويات العامة</h3>
                    <form id="general-settlements-form" class="journal-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="general-settlement-date">التاريخ *</label>
                                <input type="date" id="general-settlement-date" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="general-settlement-type">نوع التسوية *</label>
                                <select id="general-settlement-type" class="form-control" required>
                                    <option value="">اختر النوع</option>
                                    <option value="bank_reconciliation">تسوية بنكية</option>
                                    <option value="inventory_adjustment">تسوية مخزون</option>
                                    <option value="depreciation">استهلاك</option>
                                    <option value="accrual">استحقاق</option>
                                    <option value="provision">مخصص</option>
                                    <option value="correction">تصحيح خطأ</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="general-settlement-amount">المبلغ *</label>
                                <input type="number" id="general-settlement-amount" class="form-control" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="general-debit-account">الحساب المدين *</label>
                                <select id="general-debit-account" class="form-control" required>
                                    <option value="">اختر الحساب</option>
                                    <option value="cash">النقدية</option>
                                    <option value="bank">البنك</option>
                                    <option value="accounts_receivable">حسابات مدينة</option>
                                    <option value="inventory">المخزون</option>
                                    <option value="fixed_assets">أصول ثابتة</option>
                                    <option value="expenses">مصروفات</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="general-credit-account">الحساب الدائن *</label>
                                <select id="general-credit-account" class="form-control" required>
                                    <option value="">اختر الحساب</option>
                                    <option value="accounts_payable">حسابات دائنة</option>
                                    <option value="revenue">إيرادات</option>
                                    <option value="capital">رأس المال</option>
                                    <option value="retained_earnings">أرباح محتجزة</option>
                                    <option value="liabilities">التزامات</option>
                                    <option value="provisions">مخصصات</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="general-reference">رقم المرجع</label>
                                <input type="text" id="general-reference" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="general-settlement-description">وصف التسوية *</label>
                            <input type="text" id="general-settlement-description" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="general-settlement-notes">ملاحظات</label>
                            <textarea id="general-settlement-notes" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التسوية
                            </button>
                            <button type="button" onclick="Journals.clearGeneralSettlementForm()" class="btn btn-secondary">
                                <i class="fas fa-eraser"></i> مسح
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- إدراج يومية جديد -->
            <div id="new-entry-tab" class="tab-content">
                <div class="card">
                    <h3>إدراج يومية جديد</h3>
                    <form id="new-journal-form" class="journal-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="journal-date">التاريخ *</label>
                                <input type="date" id="journal-date" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="journal-reference">رقم المرجع</label>
                                <input type="text" id="journal-reference" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="journal-description">وصف الإدراج *</label>
                                <input type="text" id="journal-description" class="form-control" required>
                            </div>
                        </div>

                        <!-- إضافة قيود -->
                        <div class="journal-entries-section">
                            <h4>القيود المحاسبية</h4>

                            <div class="add-entry-form">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="entry-account">الحساب *</label>
                                        <select id="entry-account" class="form-control" required>
                                            <option value="">اختر الحساب</option>
                                            <option value="cash">النقدية</option>
                                            <option value="bank">البنك</option>
                                            <option value="accounts_receivable">حسابات مدينة</option>
                                            <option value="inventory">المخزون</option>
                                            <option value="fixed_assets">أصول ثابتة</option>
                                            <option value="accounts_payable">حسابات دائنة</option>
                                            <option value="revenue">إيرادات</option>
                                            <option value="expenses">مصروفات</option>
                                            <option value="capital">رأس المال</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="entry-description">وصف القيد</label>
                                        <input type="text" id="entry-description" class="form-control">
                                    </div>
                                    <div class="form-group">
                                        <label for="entry-debit">مدين</label>
                                        <input type="number" id="entry-debit" class="form-control" step="0.01" min="0">
                                    </div>
                                    <div class="form-group">
                                        <label for="entry-credit">دائن</label>
                                        <input type="number" id="entry-credit" class="form-control" step="0.01" min="0">
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button type="button" onclick="Journals.addJournalEntry()" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> إضافة القيد
                                    </button>
                                </div>
                            </div>

                            <!-- جدول القيود -->
                            <div class="entries-table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>الحساب</th>
                                            <th>الوصف</th>
                                            <th>مدين</th>
                                            <th>دائن</th>
                                            <th>إجراء</th>
                                        </tr>
                                    </thead>
                                    <tbody id="journal-entries-body">
                                    </tbody>
                                </table>
                            </div>

                            <!-- مجاميع القيود -->
                            <div class="entries-totals">
                                <div class="total-row">
                                    <label>إجمالي المدين:</label>
                                    <span id="total-debit">0.00 ريال</span>
                                </div>
                                <div class="total-row">
                                    <label>إجمالي الدائن:</label>
                                    <span id="total-credit">0.00 ريال</span>
                                </div>
                                <div class="total-row balance-check">
                                    <label>الفرق:</label>
                                    <span id="balance-difference">0.00 ريال</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="journal-notes">ملاحظات</label>
                            <textarea id="journal-notes" class="form-control" rows="3"></textarea>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الإدراج
                            </button>
                            <button type="button" onclick="Journals.clearJournalForm()" class="btn btn-secondary">
                                <i class="fas fa-eraser"></i> مسح النموذج
                            </button>
                            <button type="button" onclick="App.goBack()" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        this.attachEvents();
        this.loadJournals();
        this.loadCustomers();
        this.setCurrentDate();
    },

    // إضافة الأحداث
    attachEvents() {
        // تبديل التبويبات
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // نماذج اليوميات
        document.getElementById('daily-expenses-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveDailyExpense();
        });

        document.getElementById('daily-income-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveDailyIncome();
        });

        document.getElementById('customer-settlements-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCustomerSettlement();
        });

        document.getElementById('general-settlements-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveGeneralSettlement();
        });

        document.getElementById('new-journal-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveManualJournal();
        });

        // البحث والفلترة
        document.getElementById('journal-search').addEventListener('input', (e) => {
            this.searchJournals(e.target.value);
        });

        document.getElementById('journal-type-filter').addEventListener('change', (e) => {
            this.filterByType(e.target.value);
        });

        document.getElementById('journal-date-filter').addEventListener('change', (e) => {
            this.filterByDate(e.target.value);
        });
    },

    // تبديل التبويبات
    switchTab(tabName) {
        // إخفاء جميع التبويبات
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // إظهار التبويب المحدد
        document.getElementById(`${tabName}-tab`).classList.add('active');
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    },

    // تحميل اليوميات
    loadJournals() {
        const data = DB.getData();
        const journals = data.journals || [];

        const tbody = document.getElementById('journals-table-body');
        tbody.innerHTML = '';

        if (journals.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center">لا توجد إدراجات يومية</td>
                </tr>
            `;
            return;
        }

        journals.forEach(journal => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${journal.number}</td>
                <td>${new Date(journal.date).toLocaleDateString('ar-SA')}</td>
                <td>${this.getJournalTypeText(journal.type)}</td>
                <td>${journal.description}</td>
                <td>${this.formatCurrency(journal.debitAmount || 0)}</td>
                <td>${this.formatCurrency(journal.creditAmount || 0)}</td>
                <td>
                    <button onclick="Journals.viewJournal(${journal.id})" class="btn btn-sm btn-info">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button onclick="Journals.editJournal(${journal.id})" class="btn btn-sm btn-warning">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="Journals.deleteJournal(${journal.id})" class="btn btn-sm btn-danger">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    },

    // تحميل العملاء
    loadCustomers() {
        const data = DB.getData();
        const customers = data.customers || [];

        const customerSelect = document.getElementById('settlement-customer');
        if (customerSelect) {
            customerSelect.innerHTML = '<option value="">اختر العميل</option>';

            customers.forEach(customer => {
                const option = new Option(customer.name, customer.id);
                customerSelect.appendChild(option);
            });
        }
    },

    // تعيين التاريخ الحالي
    setCurrentDate() {
        const today = new Date().toISOString().split('T')[0];

        ['expense-date', 'income-date', 'settlement-date', 'general-settlement-date', 'journal-date'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.value = today;
            }
        });
    },

    // حفظ مصروف يومي
    saveDailyExpense() {
        const expenseData = {
            id: DB.getNextId('journal'),
            number: this.generateJournalNumber(),
            type: 'expense',
            date: document.getElementById('expense-date').value,
            amount: parseFloat(document.getElementById('expense-amount').value),
            category: document.getElementById('expense-category').value,
            description: document.getElementById('expense-description').value,
            notes: document.getElementById('expense-notes').value,
            debitAmount: parseFloat(document.getElementById('expense-amount').value),
            creditAmount: 0,
            createdAt: new Date().toISOString()
        };

        if (!expenseData.date || !expenseData.amount || !expenseData.category || !expenseData.description) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        const data = DB.getData();
        if (!data.journals) data.journals = [];

        data.journals.push(expenseData);
        DB.saveData(data);

        alert('تم حفظ المصروف بنجاح');
        this.clearExpenseForm();
        this.loadJournals();
    },

    // حفظ إيراد يومي
    saveDailyIncome() {
        const incomeData = {
            id: DB.getNextId('journal'),
            number: this.generateJournalNumber(),
            type: 'income',
            date: document.getElementById('income-date').value,
            amount: parseFloat(document.getElementById('income-amount').value),
            source: document.getElementById('income-source').value,
            description: document.getElementById('income-description').value,
            notes: document.getElementById('income-notes').value,
            debitAmount: 0,
            creditAmount: parseFloat(document.getElementById('income-amount').value),
            createdAt: new Date().toISOString()
        };

        if (!incomeData.date || !incomeData.amount || !incomeData.source || !incomeData.description) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        const data = DB.getData();
        if (!data.journals) data.journals = [];

        data.journals.push(incomeData);
        DB.saveData(data);

        alert('تم حفظ الإيراد بنجاح');
        this.clearIncomeForm();
        this.loadJournals();
    },

    // حفظ تسوية عميل
    saveCustomerSettlement() {
        const settlementData = {
            id: DB.getNextId('journal'),
            number: this.generateJournalNumber(),
            type: 'customer_settlement',
            date: document.getElementById('settlement-date').value,
            customerId: parseInt(document.getElementById('settlement-customer').value),
            settlementType: document.getElementById('settlement-type').value,
            amount: parseFloat(document.getElementById('settlement-amount').value),
            method: document.getElementById('settlement-method').value,
            reference: document.getElementById('settlement-reference').value,
            description: document.getElementById('settlement-description').value,
            notes: document.getElementById('settlement-notes').value,
            createdAt: new Date().toISOString()
        };

        // تحديد المدين والدائن حسب نوع التسوية
        if (settlementData.settlementType === 'payment') {
            settlementData.debitAmount = settlementData.amount;
            settlementData.creditAmount = 0;
        } else {
            settlementData.debitAmount = 0;
            settlementData.creditAmount = settlementData.amount;
        }

        if (!settlementData.date || !settlementData.customerId || !settlementData.settlementType || !settlementData.amount || !settlementData.description) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        const data = DB.getData();
        if (!data.journals) data.journals = [];

        data.journals.push(settlementData);

        // تحديث رصيد العميل
        const customer = data.customers.find(c => c.id === settlementData.customerId);
        if (customer) {
            if (settlementData.settlementType === 'payment') {
                customer.balance = (customer.balance || 0) - settlementData.amount;
            } else if (settlementData.settlementType === 'refund') {
                customer.balance = (customer.balance || 0) + settlementData.amount;
            }
        }

        DB.saveData(data);

        alert('تم حفظ التسوية بنجاح');
        this.clearSettlementForm();
        this.loadJournals();
    },

    // توليد رقم الإدراج
    generateJournalNumber() {
        const data = DB.getData();
        const nextId = data.lastIds.journal + 1;
        return `JE${nextId.toString().padStart(6, '0')}`;
    },

    // مسح نموذج المصروفات
    clearExpenseForm() {
        document.getElementById('daily-expenses-form').reset();
        document.getElementById('expense-date').value = new Date().toISOString().split('T')[0];
    },

    // مسح نموذج الإيرادات
    clearIncomeForm() {
        document.getElementById('daily-income-form').reset();
        document.getElementById('income-date').value = new Date().toISOString().split('T')[0];
    },

    // مسح نموذج التسويات
    clearSettlementForm() {
        document.getElementById('customer-settlements-form').reset();
        document.getElementById('settlement-date').value = new Date().toISOString().split('T')[0];
    },

    // الحصول على نص نوع الإدراج
    getJournalTypeText(type) {
        const types = {
            'expense': 'مصروف',
            'income': 'إيراد',
            'customer_settlement': 'تسوية عميل',
            'general_settlement': 'تسوية عامة',
            'manual': 'إدراج يدوي'
        };
        return types[type] || type;
    },

    // تنسيق العملة
    formatCurrency(amount) {
        const data = DB.getData();
        const currency = data.settings?.currency || 'ريال';
        return `${amount.toFixed(2)} ${currency}`;
    },

    // البحث في اليوميات
    searchJournals(searchTerm) {
        // تنفيذ البحث
        console.log('البحث عن:', searchTerm);
    },

    // فلترة حسب النوع
    filterByType(type) {
        // تنفيذ الفلترة
        console.log('فلترة حسب النوع:', type);
    },

    // فلترة حسب التاريخ
    filterByDate(date) {
        // تنفيذ الفلترة
        console.log('فلترة حسب التاريخ:', date);
    },

    // عرض إدراج يومية
    viewJournal(id) {
        console.log('عرض الإدراج:', id);
    },

    // تعديل إدراج يومية
    editJournal(id) {
        console.log('تعديل الإدراج:', id);
    },

    // حفظ تسوية عامة
    saveGeneralSettlement() {
        const settlementData = {
            id: DB.getNextId('journal'),
            number: this.generateJournalNumber(),
            type: 'general_settlement',
            date: document.getElementById('general-settlement-date').value,
            settlementType: document.getElementById('general-settlement-type').value,
            amount: parseFloat(document.getElementById('general-settlement-amount').value),
            debitAccount: document.getElementById('general-debit-account').value,
            creditAccount: document.getElementById('general-credit-account').value,
            reference: document.getElementById('general-reference').value,
            description: document.getElementById('general-settlement-description').value,
            notes: document.getElementById('general-settlement-notes').value,
            debitAmount: parseFloat(document.getElementById('general-settlement-amount').value),
            creditAmount: parseFloat(document.getElementById('general-settlement-amount').value),
            createdAt: new Date().toISOString()
        };

        if (!settlementData.date || !settlementData.settlementType || !settlementData.amount ||
            !settlementData.debitAccount || !settlementData.creditAccount || !settlementData.description) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        const data = DB.getData();
        if (!data.journals) data.journals = [];

        data.journals.push(settlementData);
        DB.saveData(data);

        alert('تم حفظ التسوية العامة بنجاح');
        this.clearGeneralSettlementForm();
        this.loadJournals();
    },

    // حفظ إدراج يومية يدوي
    saveManualJournal() {
        if (!this.currentJournal.entries || this.currentJournal.entries.length === 0) {
            alert('يرجى إضافة قيود محاسبية');
            return;
        }

        if (this.currentJournal.totalDebit !== this.currentJournal.totalCredit) {
            alert('إجمالي المدين يجب أن يساوي إجمالي الدائن');
            return;
        }

        const journalData = {
            id: DB.getNextId('journal'),
            number: this.generateJournalNumber(),
            type: 'manual',
            date: document.getElementById('journal-date').value,
            reference: document.getElementById('journal-reference').value,
            description: document.getElementById('journal-description').value,
            notes: document.getElementById('journal-notes').value,
            entries: [...this.currentJournal.entries],
            debitAmount: this.currentJournal.totalDebit,
            creditAmount: this.currentJournal.totalCredit,
            createdAt: new Date().toISOString()
        };

        if (!journalData.date || !journalData.description) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        const data = DB.getData();
        if (!data.journals) data.journals = [];

        data.journals.push(journalData);
        DB.saveData(data);

        alert('تم حفظ الإدراج اليومي بنجاح');
        this.clearJournalForm();
        this.loadJournals();
    },

    // إضافة قيد محاسبي
    addJournalEntry() {
        const account = document.getElementById('entry-account').value;
        const description = document.getElementById('entry-description').value;
        const debit = parseFloat(document.getElementById('entry-debit').value) || 0;
        const credit = parseFloat(document.getElementById('entry-credit').value) || 0;

        if (!account) {
            alert('يرجى اختيار الحساب');
            return;
        }

        if (debit === 0 && credit === 0) {
            alert('يرجى إدخال مبلغ في المدين أو الدائن');
            return;
        }

        if (debit > 0 && credit > 0) {
            alert('لا يمكن أن يكون للقيد مبلغ في المدين والدائن معاً');
            return;
        }

        const entry = {
            account: account,
            description: description || this.getAccountName(account),
            debit: debit,
            credit: credit
        };

        if (!this.currentJournal.entries) {
            this.currentJournal.entries = [];
        }

        this.currentJournal.entries.push(entry);
        this.renderJournalEntries();
        this.calculateJournalTotals();
        this.clearEntryForm();
    },

    // عرض القيود المحاسبية
    renderJournalEntries() {
        const tbody = document.getElementById('journal-entries-body');
        tbody.innerHTML = '';

        if (!this.currentJournal.entries || this.currentJournal.entries.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center">لا توجد قيود محاسبية</td>
                </tr>
            `;
            return;
        }

        this.currentJournal.entries.forEach((entry, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${this.getAccountName(entry.account)}</td>
                <td>${entry.description}</td>
                <td>${entry.debit > 0 ? this.formatCurrency(entry.debit) : '-'}</td>
                <td>${entry.credit > 0 ? this.formatCurrency(entry.credit) : '-'}</td>
                <td>
                    <button onclick="Journals.removeJournalEntry(${index})" class="btn btn-sm btn-danger">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    },

    // إزالة قيد محاسبي
    removeJournalEntry(index) {
        if (this.currentJournal.entries && this.currentJournal.entries[index]) {
            this.currentJournal.entries.splice(index, 1);
            this.renderJournalEntries();
            this.calculateJournalTotals();
        }
    },

    // حساب مجاميع القيود
    calculateJournalTotals() {
        if (!this.currentJournal.entries) {
            this.currentJournal.totalDebit = 0;
            this.currentJournal.totalCredit = 0;
        } else {
            this.currentJournal.totalDebit = this.currentJournal.entries.reduce((sum, entry) => sum + entry.debit, 0);
            this.currentJournal.totalCredit = this.currentJournal.entries.reduce((sum, entry) => sum + entry.credit, 0);
        }

        const difference = this.currentJournal.totalDebit - this.currentJournal.totalCredit;

        document.getElementById('total-debit').textContent = this.formatCurrency(this.currentJournal.totalDebit);
        document.getElementById('total-credit').textContent = this.formatCurrency(this.currentJournal.totalCredit);
        document.getElementById('balance-difference').textContent = this.formatCurrency(Math.abs(difference));

        // تغيير لون الفرق
        const balanceElement = document.getElementById('balance-difference').parentElement;
        if (difference === 0) {
            balanceElement.style.color = 'green';
        } else {
            balanceElement.style.color = 'red';
        }
    },

    // الحصول على اسم الحساب
    getAccountName(accountCode) {
        const accounts = {
            'cash': 'النقدية',
            'bank': 'البنك',
            'accounts_receivable': 'حسابات مدينة',
            'inventory': 'المخزون',
            'fixed_assets': 'أصول ثابتة',
            'accounts_payable': 'حسابات دائنة',
            'revenue': 'إيرادات',
            'expenses': 'مصروفات',
            'capital': 'رأس المال',
            'retained_earnings': 'أرباح محتجزة',
            'liabilities': 'التزامات',
            'provisions': 'مخصصات'
        };
        return accounts[accountCode] || accountCode;
    },

    // مسح نموذج التسويات العامة
    clearGeneralSettlementForm() {
        document.getElementById('general-settlements-form').reset();
        document.getElementById('general-settlement-date').value = new Date().toISOString().split('T')[0];
    },

    // مسح نموذج الإدراج اليدوي
    clearJournalForm() {
        document.getElementById('new-journal-form').reset();
        document.getElementById('journal-date').value = new Date().toISOString().split('T')[0];
        this.currentJournal = { entries: [], totalDebit: 0, totalCredit: 0 };
        this.renderJournalEntries();
        this.calculateJournalTotals();
    },

    // مسح نموذج القيد
    clearEntryForm() {
        document.getElementById('entry-account').value = '';
        document.getElementById('entry-description').value = '';
        document.getElementById('entry-debit').value = '';
        document.getElementById('entry-credit').value = '';
    },

    // حذف إدراج يومية
    deleteJournal(id) {
        if (confirm('هل أنت متأكد من حذف هذا الإدراج؟')) {
            const data = DB.getData();
            data.journals = data.journals.filter(j => j.id !== id);
            DB.saveData(data);
            this.loadJournals();
            alert('تم حذف الإدراج بنجاح');
        }
    }
};
