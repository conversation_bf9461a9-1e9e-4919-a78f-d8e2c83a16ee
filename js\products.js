/**
 * إدارة المنتجات
 */
const Products = {
    // عرض صفحة المنتجات
    show() {
        const content = document.getElementById('page-content');
        content.innerHTML = `
            <div class="page-header">
                <h2><i class="fas fa-box"></i> إدارة المنتجات</h2>
                <div>
                    <button id="manage-categories-btn" class="btn btn-secondary">
                        <i class="fas fa-tags"></i> إدارة الفئات
                    </button>
                    <button id="bulk-price-update-btn" class="btn btn-info">
                        <i class="fas fa-tags"></i> تحديث الأسعار المجمع
                    </button>
                    <button id="stock-alerts-btn" class="btn btn-warning">
                        <i class="fas fa-exclamation-triangle"></i> تنبيهات المخزون
                    </button>
                    <button id="add-product-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة منتج سريع
                    </button>
                    <button id="add-detailed-product-btn" class="btn btn-success">
                        <i class="fas fa-plus-circle"></i> إضافة منتج بكامل البيانات
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="search-bar">
                    <input type="text" id="product-search" class="form-control" placeholder="البحث عن منتج...">
                    <select id="category-filter" class="form-control">
                        <option value="">جميع الفئات</option>
                    </select>
                    <select id="stock-filter" class="form-control">
                        <option value="">جميع المنتجات</option>
                        <option value="low">مخزون منخفض</option>
                        <option value="out">نفد المخزون</option>
                    </select>
                </div>

                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>كود الصنف</th>
                                <th>اسم الصنف</th>
                                <th>وحدة الصنف</th>
                                <th>المجموعة</th>
                                <th>إعادة الطلب</th>
                                <th>الحد الأدنى</th>
                                <th>الكمية الحالية</th>
                                <th>سعر الشراء</th>
                                <th>سعر البيع</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="products-table-body">
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- نموذج إضافة/تعديل المنتج -->
            <div id="product-modal" class="modal hidden">
                <div class="modal-content product-form-modal">
                    <div class="modal-header">
                        <h3 id="modal-title">الأصناف</h3>
                        <button id="close-modal" class="btn-close" onclick="closeModal('product-modal')">&times;</button>
                    </div>
                    <form id="product-form" class="product-form-grid">
                        <!-- الصف الأول -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="product-code">كود الصنف</label>
                                <input type="text" id="product-code" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="product-name">اسم الصنف</label>
                                <input type="text" id="product-name" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="product-unit">وحدة الصنف</label>
                                <select id="product-unit" class="form-control">
                                    <option value="قطعة">قطعة</option>
                                    <option value="كيلو">كيلو</option>
                                    <option value="لتر">لتر</option>
                                    <option value="متر">متر</option>
                                    <option value="علبة">علبة</option>
                                    <option value="كرتون">كرتون</option>
                                    <option value="طن">طن</option>
                                    <option value="جرام">جرام</option>
                                </select>
                            </div>
                        </div>

                        <!-- الصف الثاني -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="product-category">المجموعة</label>
                                <select id="product-category" class="form-control">
                                    <option value="">اختر المجموعة</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="product-reorder-level">إعادة الطلب</label>
                                <input type="number" id="product-reorder-level" class="form-control" min="0" value="5">
                            </div>
                            <div class="form-group">
                                <label for="product-min-stock">الحد الأدنى</label>
                                <input type="number" id="product-min-stock" class="form-control" min="0" value="5">
                            </div>
                        </div>

                        <!-- الصف الثالث -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="product-purchase-price">سعر الشراء</label>
                                <input type="number" id="product-purchase-price" class="form-control" step="0.01" min="0">
                            </div>
                            <div class="form-group">
                                <label for="product-sale-price">سعر البيع</label>
                                <input type="number" id="product-sale-price" class="form-control" step="0.01" min="0" required>
                            </div>
                            <div class="form-group">
                                <label for="product-quantity">الكمية الحالية</label>
                                <input type="number" id="product-quantity" class="form-control" min="0" value="0">
                            </div>
                        </div>

                        <!-- الصف الرابع -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="product-barcode">الباركود</label>
                                <input type="text" id="product-barcode" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="product-location">موقع التخزين</label>
                                <input type="text" id="product-location" class="form-control" placeholder="مثال: رف A1">
                            </div>
                            <div class="form-group">
                                <label for="product-supplier">المورد الرئيسي</label>
                                <select id="product-supplier" class="form-control">
                                    <option value="">اختر المورد</option>
                                </select>
                            </div>
                        </div>

                        <!-- الصف الخامس -->
                        <div class="form-row full-width">
                            <div class="form-group">
                                <label for="product-description">الوصف</label>
                                <textarea id="product-description" class="form-control" rows="3" placeholder="وصف تفصيلي للصنف..."></textarea>
                            </div>
                        </div>

                        <!-- الصف السادس -->
                        <div class="form-row full-width">
                            <div class="form-group">
                                <label for="product-notes">ملاحظات</label>
                                <textarea id="product-notes" class="form-control" rows="2" placeholder="ملاحظات إضافية..."></textarea>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الصنف
                            </button>
                            <button type="button" id="cancel-btn" class="btn btn-secondary" onclick="closeModal('product-modal')">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                            <button type="button" id="clear-form-btn" class="btn btn-warning">
                                <i class="fas fa-eraser"></i> مسح النموذج
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- نموذج إدارة الفئات -->
            <div id="categories-modal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>إدارة الفئات</h3>
                        <button id="close-categories-modal" class="btn-close" onclick="closeModal('categories-modal')">&times;</button>
                    </div>
                    <div style="padding: 20px;">
                        <div class="form-group">
                            <label for="new-category-name">اسم الفئة الجديدة</label>
                            <input type="text" id="new-category-name" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="new-category-description">وصف الفئة</label>
                            <input type="text" id="new-category-description" class="form-control">
                        </div>
                        <button id="add-category-btn" class="btn btn-primary">إضافة فئة</button>

                        <div class="categories-list" style="margin-top: 20px;">
                            <h4>الفئات الموجودة</h4>
                            <div id="categories-list-content"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج تعديل المخزون -->
            <div id="stock-modal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>تعديل المخزون</h3>
                        <button id="close-stock-modal" class="btn-close" onclick="closeModal('stock-modal')">&times;</button>
                    </div>
                    <div style="padding: 20px;">
                        <div class="form-group">
                            <label>المنتج:</label>
                            <span id="stock-product-name" style="font-weight: bold;"></span>
                        </div>
                        <div class="form-group">
                            <label>الكمية الحالية:</label>
                            <span id="current-stock" style="font-weight: bold; color: var(--primary-color);"></span>
                        </div>
                        <div class="form-group">
                            <label for="stock-adjustment">نوع التعديل</label>
                            <select id="stock-adjustment" class="form-control">
                                <option value="add">إضافة للمخزون</option>
                                <option value="subtract">خصم من المخزون</option>
                                <option value="set">تعيين كمية جديدة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="stock-quantity">الكمية</label>
                            <input type="number" id="stock-quantity" class="form-control" min="0" required>
                        </div>
                        <div class="form-group">
                            <label for="stock-reason">سبب التعديل</label>
                            <input type="text" id="stock-reason" class="form-control" placeholder="اختياري">
                        </div>
                        <div class="form-actions">
                            <button id="save-stock-btn" class="btn btn-primary">حفظ التعديل</button>
                            <button id="cancel-stock-btn" class="btn btn-secondary" onclick="closeModal('stock-modal')">إلغاء</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج تحديث الأسعار المجمع -->
            <div id="bulk-price-modal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>تحديث الأسعار بشكل مجمع</h3>
                        <button id="close-bulk-price-modal" class="btn-close" onclick="Products.closeBulkPriceModal()">&times;</button>
                    </div>
                    <div style="padding: 20px;">
                        <div class="form-group">
                            <label for="bulk-category">الفئة (اختياري)</label>
                            <select id="bulk-category" class="form-control">
                                <option value="">جميع الفئات</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="bulk-update-type">نوع التحديث</label>
                            <select id="bulk-update-type" class="form-control">
                                <option value="percentage">نسبة مئوية</option>
                                <option value="fixed">مبلغ ثابت</option>
                                <option value="set">تعيين سعر جديد</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="bulk-price-type">نوع السعر</label>
                            <select id="bulk-price-type" class="form-control">
                                <option value="sale">سعر البيع</option>
                                <option value="purchase">سعر الشراء</option>
                                <option value="both">كلا السعرين</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="bulk-value">القيمة</label>
                            <input type="number" id="bulk-value" class="form-control" step="0.01" required>
                            <small class="form-text">للنسبة المئوية: أدخل الرقم بدون علامة % (مثال: 10 للزيادة 10%)</small>
                        </div>
                        <div class="form-actions">
                            <button onclick="Products.applyBulkPriceUpdate()" class="btn btn-primary">تطبيق التحديث</button>
                            <button id="cancel-bulk-price" onclick="Products.closeBulkPriceModal()" class="btn btn-secondary">إلغاء</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج تنبيهات المخزون -->
            <div id="stock-alerts-modal" class="modal hidden">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3>تنبيهات المخزون</h3>
                        <button id="close-stock-alerts-modal" class="btn-close" onclick="Products.closeStockAlertsModal()">&times;</button>
                    </div>
                    <div style="padding: 20px;">
                        <div class="alert-summary">
                            <div class="alert-stats">
                                <div class="alert-stat out-of-stock">
                                    <i class="fas fa-times-circle"></i>
                                    <span id="out-of-stock-count">0</span>
                                    <label>نفد المخزون</label>
                                </div>
                                <div class="alert-stat low-stock">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <span id="low-stock-count">0</span>
                                    <label>مخزون منخفض</label>
                                </div>
                                <div class="alert-stat reorder-level">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span id="reorder-count">0</span>
                                    <label>إعادة طلب</label>
                                </div>
                            </div>
                        </div>
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>اسم المنتج</th>
                                        <th>الفئة</th>
                                        <th>الكمية الحالية</th>
                                        <th>الحد الأدنى</th>
                                        <th>إعادة الطلب</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="stock-alerts-table">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج إضافة منتج بكامل البيانات -->
            <div id="detailed-product-modal" class="modal hidden">
                <div class="modal-content extra-large">
                    <div class="modal-header">
                        <h3>إضافة منتج بكامل البيانات</h3>
                        <button id="close-detailed-product-modal" class="btn-close" onclick="Products.closeDetailedProductModal()">&times;</button>
                    </div>
                    <form id="detailed-product-form" class="detailed-product-form">
                        <!-- معلومات أساسية -->
                        <div class="form-section">
                            <h4><i class="fas fa-info-circle"></i> المعلومات الأساسية</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-product-code">كود المنتج *</label>
                                    <input type="text" id="detailed-product-code" class="form-control" required>
                                    <button type="button" onclick="Products.generateProductCode()" class="btn btn-sm btn-secondary">توليد تلقائي</button>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-product-name">اسم المنتج *</label>
                                    <input type="text" id="detailed-product-name" class="form-control" required>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-product-name-en">الاسم بالإنجليزية</label>
                                    <input type="text" id="detailed-product-name-en" class="form-control">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="detailed-product-description">الوصف التفصيلي</label>
                                <textarea id="detailed-product-description" class="form-control" rows="3" placeholder="وصف شامل للمنتج، مواصفاته، استخداماته..."></textarea>
                            </div>
                        </div>

                        <!-- التصنيف والفئات -->
                        <div class="form-section">
                            <h4><i class="fas fa-tags"></i> التصنيف والفئات</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-product-category">الفئة الرئيسية *</label>
                                    <select id="detailed-product-category" class="form-control" required>
                                        <option value="">اختر الفئة</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-product-subcategory">الفئة الفرعية</label>
                                    <input type="text" id="detailed-product-subcategory" class="form-control" placeholder="مثال: إلكترونيات > هواتف > ذكية">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-product-brand">العلامة التجارية</label>
                                    <input type="text" id="detailed-product-brand" class="form-control" placeholder="مثال: سامسونج، آبل، إلخ">
                                </div>
                            </div>
                        </div>

                        <!-- الأسعار والتكلفة -->
                        <div class="form-section">
                            <h4><i class="fas fa-money-bill-wave"></i> الأسعار والتكلفة</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-purchase-price">سعر الشراء *</label>
                                    <input type="number" id="detailed-purchase-price" class="form-control" step="0.01" min="0" required>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-sale-price">سعر البيع *</label>
                                    <input type="number" id="detailed-sale-price" class="form-control" step="0.01" min="0" required>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-wholesale-price">سعر الجملة</label>
                                    <input type="number" id="detailed-wholesale-price" class="form-control" step="0.01" min="0">
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-profit-margin">هامش الربح (%)</label>
                                    <input type="number" id="detailed-profit-margin" class="form-control" step="0.01" min="0" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-tax-rate">معدل الضريبة (%)</label>
                                    <input type="number" id="detailed-tax-rate" class="form-control" step="0.01" min="0" value="15">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-discount-allowed">خصم مسموح (%)</label>
                                    <input type="number" id="detailed-discount-allowed" class="form-control" step="0.01" min="0" max="100" value="0">
                                </div>
                            </div>
                        </div>

                        <!-- المخزون والكميات -->
                        <div class="form-section">
                            <h4><i class="fas fa-warehouse"></i> المخزون والكميات</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-unit">وحدة القياس *</label>
                                    <select id="detailed-unit" class="form-control" required>
                                        <option value="قطعة">قطعة</option>
                                        <option value="كيلو">كيلو</option>
                                        <option value="لتر">لتر</option>
                                        <option value="متر">متر</option>
                                        <option value="علبة">علبة</option>
                                        <option value="كرتون">كرتون</option>
                                        <option value="طن">طن</option>
                                        <option value="جرام">جرام</option>
                                        <option value="باكيت">باكيت</option>
                                        <option value="زجاجة">زجاجة</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-initial-quantity">الكمية الابتدائية *</label>
                                    <input type="number" id="detailed-initial-quantity" class="form-control" min="0" value="0" required>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-min-stock">الحد الأدنى للمخزون *</label>
                                    <input type="number" id="detailed-min-stock" class="form-control" min="0" value="5" required>
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-reorder-level">مستوى إعادة الطلب *</label>
                                    <input type="number" id="detailed-reorder-level" class="form-control" min="0" value="10" required>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-max-stock">الحد الأقصى للمخزون</label>
                                    <input type="number" id="detailed-max-stock" class="form-control" min="0" value="1000">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-location">موقع التخزين</label>
                                    <input type="text" id="detailed-location" class="form-control" placeholder="مثال: رف A1، مخزن 2">
                                </div>
                            </div>
                        </div>

                        <!-- معلومات المورد -->
                        <div class="form-section">
                            <h4><i class="fas fa-truck"></i> معلومات المورد</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-supplier">المورد الرئيسي</label>
                                    <select id="detailed-supplier" class="form-control">
                                        <option value="">اختر المورد</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-code">كود المنتج عند المورد</label>
                                    <input type="text" id="detailed-supplier-code" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-lead-time">مدة التوريد (أيام)</label>
                                    <input type="number" id="detailed-lead-time" class="form-control" min="0" value="7">
                                </div>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="form-section">
                            <h4><i class="fas fa-info"></i> معلومات إضافية</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-barcode">الباركود</label>
                                    <input type="text" id="detailed-barcode" class="form-control">
                                    <button type="button" onclick="Products.generateBarcode()" class="btn btn-sm btn-secondary">توليد باركود</button>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-weight">الوزن (كجم)</label>
                                    <input type="number" id="detailed-weight" class="form-control" step="0.01" min="0">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-dimensions">الأبعاد (طول×عرض×ارتفاع)</label>
                                    <input type="text" id="detailed-dimensions" class="form-control" placeholder="مثال: 10×5×3 سم">
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-expiry-date">تاريخ انتهاء الصلاحية</label>
                                    <input type="date" id="detailed-expiry-date" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-warranty">فترة الضمان (شهور)</label>
                                    <input type="number" id="detailed-warranty" class="form-control" min="0" value="0">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-status">حالة المنتج</label>
                                    <select id="detailed-status" class="form-control">
                                        <option value="active">نشط</option>
                                        <option value="inactive">غير نشط</option>
                                        <option value="discontinued">متوقف</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="detailed-notes">ملاحظات إضافية</label>
                                <textarea id="detailed-notes" class="form-control" rows="3" placeholder="أي ملاحظات أو تعليمات خاصة بالمنتج..."></textarea>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ المنتج
                            </button>
                            <button type="button" onclick="Products.closeDetailedProductModal()" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                            <button type="button" onclick="Products.clearDetailedForm()" class="btn btn-warning">
                                <i class="fas fa-eraser"></i> مسح النموذج
                            </button>
                            <button type="button" onclick="Products.saveAndAddAnother()" class="btn btn-info">
                                <i class="fas fa-plus"></i> حفظ وإضافة آخر
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        this.attachEvents();
        this.loadProducts();
        this.loadCategories();
    },

    // إضافة الأحداث
    attachEvents() {
        // زر إضافة منتج جديد (سريع)
        document.getElementById('add-product-btn').addEventListener('click', () => {
            this.showProductModal();
        });

        // زر إضافة منتج بكامل البيانات
        document.getElementById('add-detailed-product-btn').addEventListener('click', () => {
            this.showDetailedProductModal();
        });

        // زر إدارة الفئات
        document.getElementById('manage-categories-btn').addEventListener('click', () => {
            this.showCategoriesModal();
        });

        // زر تحديث الأسعار المجمع
        document.getElementById('bulk-price-update-btn').addEventListener('click', () => {
            this.showBulkPriceModal();
        });

        // زر تنبيهات المخزون
        document.getElementById('stock-alerts-btn').addEventListener('click', () => {
            this.showStockAlertsModal();
        });

        // البحث والفلترة
        document.getElementById('product-search').addEventListener('input', (e) => {
            this.searchProducts(e.target.value);
        });

        document.getElementById('category-filter').addEventListener('change', (e) => {
            this.filterByCategory(e.target.value);
        });

        document.getElementById('stock-filter').addEventListener('change', (e) => {
            this.filterByStock(e.target.value);
        });

        // معالجات خاصة بوحدة المنتجات
        document.addEventListener('click', (e) => {
            // زر إضافة فئة
            if (e.target.id === 'add-category-btn') {
                e.preventDefault();
                this.addCategory();
            }

            // زر حفظ تعديل المخزون
            if (e.target.id === 'save-stock-btn') {
                e.preventDefault();
                this.saveStockAdjustment();
            }

            // زر مسح النموذج
            if (e.target.id === 'clear-form-btn') {
                e.preventDefault();
                this.clearProductForm();
            }

            // أزرار الإغلاق الإضافية
            if (e.target.id === 'close-bulk-price-modal') {
                e.preventDefault();
                closeModal('bulk-price-modal');
            }

            if (e.target.id === 'close-stock-alerts-modal') {
                e.preventDefault();
                closeModal('stock-alerts-modal');
            }
        });

        // نموذج المنتج
        document.getElementById('product-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveProduct();
        });

        // نموذج المنتج المفصل
        document.getElementById('detailed-product-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveDetailedProduct();
        });

        // حساب هامش الربح تلقائياً
        document.getElementById('detailed-purchase-price').addEventListener('input', () => {
            this.calculateProfitMargin();
        });
        document.getElementById('detailed-sale-price').addEventListener('input', () => {
            this.calculateProfitMargin();
        });
    },

    // تحميل المنتجات
    loadProducts() {
        const data = DB.getData();
        const products = data.products || [];
        this.renderProducts(products);
    },

    // عرض المنتجات في الجدول
    renderProducts(products) {
        const tbody = document.getElementById('products-table-body');
        tbody.innerHTML = '';

        if (products.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="11" class="text-center">لا توجد أصناف مسجلة</td>
                </tr>
            `;
            return;
        }

        const data = DB.getData();
        products.forEach(product => {
            const category = data.categories.find(c => c.id === product.categoryId);
            const supplier = data.suppliers?.find(s => s.id === product.supplierId);
            const row = document.createElement('tr');

            // تحديد حالة المخزون
            let stockStatus = 'جيد';
            let stockClass = 'good';

            if (product.quantity === 0) {
                stockStatus = 'نفد المخزون';
                stockClass = 'out';
            } else if (product.quantity <= (product.reorderLevel || 5)) {
                stockStatus = 'إعادة طلب';
                stockClass = 'reorder';
            } else if (product.quantity <= (product.minStock || 5)) {
                stockStatus = 'منخفض';
                stockClass = 'low';
            }

            row.innerHTML = `
                <td>${product.code || '-'}</td>
                <td>${product.name}</td>
                <td>${product.unit || 'قطعة'}</td>
                <td>${category ? category.name : 'غير محدد'}</td>
                <td>${product.reorderLevel || 5}</td>
                <td>${product.minStock || 5}</td>
                <td>${product.quantity || 0}</td>
                <td>${this.formatCurrency(product.purchasePrice || 0)}</td>
                <td>${this.formatCurrency(product.salePrice || 0)}</td>
                <td><span class="stock-status ${stockClass}">${stockStatus}</span></td>
                <td class="actions">
                    <button onclick="Products.editProduct(${product.id})" class="btn btn-sm btn-warning" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="Products.deleteProduct(${product.id})" class="btn btn-sm btn-danger" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button onclick="Products.adjustStock(${product.id})" class="btn btn-sm btn-info" title="تعديل المخزون">
                        <i class="fas fa-warehouse"></i>
                    </button>
                    <button onclick="Products.viewDetails(${product.id})" class="btn btn-sm btn-secondary" title="التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    },

    // تحميل الفئات
    loadCategories() {
        const data = DB.getData();
        const categories = data.categories || [];

        // تحديث قائمة الفئات في النموذج
        const categorySelect = document.getElementById('product-category');
        const categoryFilter = document.getElementById('category-filter');

        categorySelect.innerHTML = '<option value="">اختر الفئة</option>';
        categoryFilter.innerHTML = '<option value="">جميع الفئات</option>';

        categories.forEach(category => {
            const option1 = new Option(category.name, category.id);
            const option2 = new Option(category.name, category.id);
            categorySelect.appendChild(option1);
            categoryFilter.appendChild(option2);
        });

        // تحديث قائمة الفئات في نموذج الإدارة
        this.renderCategoriesList();
    },

    // عرض قائمة الفئات
    renderCategoriesList() {
        const data = DB.getData();
        const categories = data.categories || [];
        const container = document.getElementById('categories-list-content');

        if (!container) return;

        container.innerHTML = '';

        categories.forEach(category => {
            const item = document.createElement('div');
            item.className = 'category-item';
            item.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; border: 1px solid #ddd; margin-bottom: 5px; border-radius: 5px;">
                    <div>
                        <strong>${category.name}</strong>
                        ${category.description ? `<br><small>${category.description}</small>` : ''}
                    </div>
                    <button onclick="Products.deleteCategory(${category.id})" class="btn btn-sm btn-danger">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            container.appendChild(item);
        });
    },

    // البحث عن المنتجات
    searchProducts(query) {
        const products = DB.search('products', query, ['name', 'description', 'barcode']);
        this.renderProducts(products);
    },

    // فلترة حسب الفئة
    filterByCategory(categoryId) {
        const data = DB.getData();
        let products = data.products || [];

        if (categoryId) {
            products = products.filter(p => p.categoryId == categoryId);
        }

        this.renderProducts(products);
    },

    // فلترة حسب حالة المخزون
    filterByStock(stockType) {
        const data = DB.getData();
        let products = data.products || [];

        if (stockType === 'low') {
            products = products.filter(p => p.quantity <= (p.minStock || 5) && p.quantity > 0);
        } else if (stockType === 'out') {
            products = products.filter(p => p.quantity === 0);
        }

        this.renderProducts(products);
    },

    // عرض نموذج المنتج
    showProductModal(product = null) {
        const modal = document.getElementById('product-modal');
        const title = document.getElementById('modal-title');
        const form = document.getElementById('product-form');

        if (product) {
            title.textContent = 'تعديل الصنف';
            document.getElementById('product-code').value = product.code || '';
            document.getElementById('product-name').value = product.name || '';
            document.getElementById('product-unit').value = product.unit || 'قطعة';
            document.getElementById('product-category').value = product.categoryId || '';
            document.getElementById('product-reorder-level').value = product.reorderLevel || 5;
            document.getElementById('product-min-stock').value = product.minStock || 5;
            document.getElementById('product-purchase-price').value = product.purchasePrice || 0;
            document.getElementById('product-sale-price').value = product.salePrice || 0;
            document.getElementById('product-quantity').value = product.quantity || 0;
            document.getElementById('product-barcode').value = product.barcode || '';
            document.getElementById('product-location').value = product.location || '';
            document.getElementById('product-supplier').value = product.supplierId || '';
            document.getElementById('product-description').value = product.description || '';
            document.getElementById('product-notes').value = product.notes || '';
            form.dataset.productId = product.id;
        } else {
            title.textContent = 'إضافة صنف جديد';
            this.clearProductForm();
            delete form.dataset.productId;
        }

        // تحميل قوائم الموردين
        this.loadSuppliers();
        modal.classList.remove('hidden');
    },

    // مسح النموذج
    clearProductForm() {
        const form = document.getElementById('product-form');
        form.reset();

        // تعيين القيم الافتراضية
        document.getElementById('product-unit').value = 'قطعة';
        document.getElementById('product-reorder-level').value = 5;
        document.getElementById('product-min-stock').value = 5;
        document.getElementById('product-quantity').value = 0;

        // إزالة رسائل الخطأ
        document.querySelectorAll('.form-group.has-error').forEach(group => {
            group.classList.remove('has-error');
        });

        // تركيز على أول حقل
        document.getElementById('product-code').focus();
    },

    // إخفاء نموذج المنتج
    hideProductModal() {
        document.getElementById('product-modal').classList.add('hidden');
    },

    // حفظ المنتج
    saveProduct() {
        const form = document.getElementById('product-form');
        const data = DB.getData();

        // جمع بيانات النموذج
        const productData = {
            code: document.getElementById('product-code').value.trim(),
            name: document.getElementById('product-name').value.trim(),
            unit: document.getElementById('product-unit').value,
            categoryId: parseInt(document.getElementById('product-category').value) || null,
            reorderLevel: parseInt(document.getElementById('product-reorder-level').value) || 5,
            minStock: parseInt(document.getElementById('product-min-stock').value) || 5,
            purchasePrice: parseFloat(document.getElementById('product-purchase-price').value) || 0,
            salePrice: parseFloat(document.getElementById('product-sale-price').value) || 0,
            quantity: parseInt(document.getElementById('product-quantity').value) || 0,
            barcode: document.getElementById('product-barcode').value.trim(),
            location: document.getElementById('product-location').value.trim(),
            supplierId: parseInt(document.getElementById('product-supplier').value) || null,
            description: document.getElementById('product-description').value.trim(),
            notes: document.getElementById('product-notes').value.trim()
        };

        // التحقق من صحة البيانات
        if (!this.validateProductData(productData)) {
            return;
        }

        // التحقق من عدم تكرار الكود
        if (!form.dataset.productId) {
            const existingProduct = data.products.find(p => p.code === productData.code);
            if (existingProduct) {
                this.showFieldError('product-code', 'كود الصنف موجود مسبقاً');
                return;
            }
        }

        if (form.dataset.productId) {
            // تعديل منتج موجود
            const productId = parseInt(form.dataset.productId);
            const productIndex = data.products.findIndex(p => p.id === productId);

            if (productIndex !== -1) {
                data.products[productIndex] = {
                    ...data.products[productIndex],
                    ...productData,
                    updatedAt: new Date().toISOString()
                };
            }
        } else {
            // إضافة منتج جديد
            const newProduct = {
                id: DB.getNextId('product'),
                ...productData,
                createdAt: new Date().toISOString()
            };
            data.products.push(newProduct);
        }

        DB.saveData(data);
        this.hideProductModal();
        this.loadProducts();

        const message = form.dataset.productId ? 'تم تعديل الصنف بنجاح' : 'تم إضافة الصنف بنجاح';
        alert(message);
    },

    // التحقق من صحة بيانات المنتج
    validateProductData(productData) {
        let isValid = true;

        // مسح رسائل الخطأ السابقة
        document.querySelectorAll('.form-group.has-error').forEach(group => {
            group.classList.remove('has-error');
        });

        // التحقق من الحقول المطلوبة
        if (!productData.code) {
            this.showFieldError('product-code', 'كود الصنف مطلوب');
            isValid = false;
        }

        if (!productData.name) {
            this.showFieldError('product-name', 'اسم الصنف مطلوب');
            isValid = false;
        }

        if (productData.salePrice <= 0) {
            this.showFieldError('product-sale-price', 'سعر البيع يجب أن يكون أكبر من صفر');
            isValid = false;
        }

        if (productData.purchasePrice < 0) {
            this.showFieldError('product-purchase-price', 'سعر الشراء لا يمكن أن يكون سالباً');
            isValid = false;
        }

        if (productData.quantity < 0) {
            this.showFieldError('product-quantity', 'الكمية لا يمكن أن تكون سالبة');
            isValid = false;
        }

        return isValid;
    },

    // عرض رسالة خطأ للحقل
    showFieldError(fieldId, message) {
        const field = document.getElementById(fieldId);
        const formGroup = field.closest('.form-group');

        formGroup.classList.add('has-error');

        let errorElement = formGroup.querySelector('.error-message');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'error-message';
            formGroup.appendChild(errorElement);
        }

        errorElement.textContent = message;
        field.focus();
    },

    // تحميل الموردين
    loadSuppliers() {
        const data = DB.getData();
        const suppliers = data.suppliers || [];
        const supplierSelect = document.getElementById('product-supplier');

        supplierSelect.innerHTML = '<option value="">اختر المورد</option>';

        suppliers.forEach(supplier => {
            const option = new Option(supplier.name, supplier.id);
            supplierSelect.appendChild(option);
        });
    },

    // عرض تفاصيل المنتج
    viewDetails(id) {
        const data = DB.getData();
        const product = data.products.find(p => p.id === id);
        if (!product) return;

        const category = data.categories.find(c => c.id === product.categoryId);
        const supplier = data.suppliers?.find(s => s.id === product.supplierId);

        const detailsHtml = `
            <div class="product-details">
                <h3>تفاصيل الصنف: ${product.name}</h3>
                <div class="details-grid">
                    <div class="detail-item">
                        <label>كود الصنف:</label>
                        <span>${product.code || '-'}</span>
                    </div>
                    <div class="detail-item">
                        <label>اسم الصنف:</label>
                        <span>${product.name}</span>
                    </div>
                    <div class="detail-item">
                        <label>وحدة الصنف:</label>
                        <span>${product.unit || 'قطعة'}</span>
                    </div>
                    <div class="detail-item">
                        <label>المجموعة:</label>
                        <span>${category ? category.name : 'غير محدد'}</span>
                    </div>
                    <div class="detail-item">
                        <label>سعر الشراء:</label>
                        <span>${this.formatCurrency(product.purchasePrice || 0)}</span>
                    </div>
                    <div class="detail-item">
                        <label>سعر البيع:</label>
                        <span>${this.formatCurrency(product.salePrice || 0)}</span>
                    </div>
                    <div class="detail-item">
                        <label>الكمية الحالية:</label>
                        <span>${product.quantity || 0}</span>
                    </div>
                    <div class="detail-item">
                        <label>إعادة الطلب:</label>
                        <span>${product.reorderLevel || 5}</span>
                    </div>
                    <div class="detail-item">
                        <label>الحد الأدنى:</label>
                        <span>${product.minStock || 5}</span>
                    </div>
                    <div class="detail-item">
                        <label>الباركود:</label>
                        <span>${product.barcode || '-'}</span>
                    </div>
                    <div class="detail-item">
                        <label>موقع التخزين:</label>
                        <span>${product.location || '-'}</span>
                    </div>
                    <div class="detail-item">
                        <label>المورد الرئيسي:</label>
                        <span>${supplier ? supplier.name : '-'}</span>
                    </div>
                    ${product.description ? `
                    <div class="detail-item full-width">
                        <label>الوصف:</label>
                        <span>${product.description}</span>
                    </div>
                    ` : ''}
                    ${product.notes ? `
                    <div class="detail-item full-width">
                        <label>ملاحظات:</label>
                        <span>${product.notes}</span>
                    </div>
                    ` : ''}
                    <div class="detail-item">
                        <label>تاريخ الإنشاء:</label>
                        <span>${new Date(product.createdAt).toLocaleDateString('ar-SA')}</span>
                    </div>
                    ${product.updatedAt ? `
                    <div class="detail-item">
                        <label>آخر تحديث:</label>
                        <span>${new Date(product.updatedAt).toLocaleDateString('ar-SA')}</span>
                    </div>
                    ` : ''}
                </div>
                <div class="details-actions">
                    <button onclick="Products.editProduct(${product.id})" class="btn btn-primary">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button onclick="Products.adjustStock(${product.id})" class="btn btn-info">
                        <i class="fas fa-warehouse"></i> تعديل المخزون
                    </button>
                    <button onclick="document.getElementById('details-modal').classList.add('hidden')" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إغلاق
                    </button>
                </div>
            </div>
        `;

        // إنشاء نافذة التفاصيل إذا لم تكن موجودة
        let detailsModal = document.getElementById('details-modal');
        if (!detailsModal) {
            detailsModal = document.createElement('div');
            detailsModal.id = 'details-modal';
            detailsModal.className = 'modal';
            detailsModal.innerHTML = `
                <div class="modal-content">
                    <div id="details-content"></div>
                </div>
            `;
            document.body.appendChild(detailsModal);
        }

        document.getElementById('details-content').innerHTML = detailsHtml;
        detailsModal.classList.remove('hidden');
    },

    // تعديل المنتج
    editProduct(id) {
        const data = DB.getData();
        const product = data.products.find(p => p.id === id);
        if (product) {
            this.showProductModal(product);
        }
    },

    // حذف المنتج
    deleteProduct(id) {
        if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
            const data = DB.getData();
            data.products = data.products.filter(p => p.id !== id);
            DB.saveData(data);
            this.loadProducts();
            alert('تم حذف المنتج بنجاح');
        }
    },

    // تعديل المخزون
    adjustStock(id) {
        const data = DB.getData();
        const product = data.products.find(p => p.id === id);
        if (!product) return;

        document.getElementById('stock-product-name').textContent = product.name;
        document.getElementById('current-stock').textContent = product.quantity;
        document.getElementById('stock-quantity').value = '';
        document.getElementById('stock-reason').value = '';
        document.getElementById('stock-adjustment').value = 'add';

        const modal = document.getElementById('stock-modal');
        modal.classList.remove('hidden');
        modal.dataset.productId = id;
    },

    // حفظ تعديل المخزون
    saveStockAdjustment() {
        const modal = document.getElementById('stock-modal');
        const productId = parseInt(modal.dataset.productId);
        const adjustmentType = document.getElementById('stock-adjustment').value;
        const quantity = parseInt(document.getElementById('stock-quantity').value);
        const reason = document.getElementById('stock-reason').value.trim();

        if (!quantity || quantity < 0) {
            alert('يرجى إدخال كمية صحيحة');
            return;
        }

        const data = DB.getData();
        const productIndex = data.products.findIndex(p => p.id === productId);

        if (productIndex === -1) return;

        const product = data.products[productIndex];
        let newQuantity = product.quantity;

        switch (adjustmentType) {
            case 'add':
                newQuantity += quantity;
                break;
            case 'subtract':
                newQuantity = Math.max(0, newQuantity - quantity);
                break;
            case 'set':
                newQuantity = quantity;
                break;
        }

        data.products[productIndex].quantity = newQuantity;

        // تسجيل حركة المخزون (يمكن تطويرها لاحقاً)
        const stockMovement = {
            id: DB.getNextId('transaction'),
            productId: productId,
            type: adjustmentType,
            quantity: quantity,
            oldQuantity: product.quantity,
            newQuantity: newQuantity,
            reason: reason,
            date: new Date().toISOString()
        };

        if (!data.stockMovements) {
            data.stockMovements = [];
        }
        data.stockMovements.push(stockMovement);

        DB.saveData(data);
        this.hideStockModal();
        this.loadProducts();
        alert('تم تعديل المخزون بنجاح');
    },

    // إخفاء نموذج المخزون
    hideStockModal() {
        document.getElementById('stock-modal').classList.add('hidden');
    },

    // عرض نموذج إدارة الفئات
    showCategoriesModal() {
        this.renderCategoriesList();
        document.getElementById('categories-modal').classList.remove('hidden');
    },

    // إخفاء نموذج إدارة الفئات
    hideCategoriesModal() {
        document.getElementById('categories-modal').classList.add('hidden');
    },

    // إضافة فئة جديدة
    addCategory() {
        const name = document.getElementById('new-category-name').value.trim();
        const description = document.getElementById('new-category-description').value.trim();

        if (!name) {
            alert('يرجى إدخال اسم الفئة');
            return;
        }

        const data = DB.getData();
        const newCategory = {
            id: DB.getNextId('category'),
            name: name,
            description: description,
            createdAt: new Date().toISOString()
        };

        data.categories.push(newCategory);
        DB.saveData(data);

        // مسح النموذج
        document.getElementById('new-category-name').value = '';
        document.getElementById('new-category-description').value = '';

        // تحديث القوائم
        this.loadCategories();
        alert('تم إضافة الفئة بنجاح');
    },

    // حذف فئة
    deleteCategory(id) {
        if (id === 1) {
            alert('لا يمكن حذف الفئة الافتراضية');
            return;
        }

        if (confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
            const data = DB.getData();

            // التحقق من وجود منتجات في هذه الفئة
            const productsInCategory = data.products.filter(p => p.categoryId === id);
            if (productsInCategory.length > 0) {
                if (!confirm(`توجد ${productsInCategory.length} منتجات في هذه الفئة. سيتم نقلها للفئة العامة. هل تريد المتابعة؟`)) {
                    return;
                }

                // نقل المنتجات للفئة العامة
                data.products.forEach(product => {
                    if (product.categoryId === id) {
                        product.categoryId = 1; // الفئة العامة
                    }
                });
            }

            data.categories = data.categories.filter(c => c.id !== id);
            DB.saveData(data);
            this.loadCategories();
            this.loadProducts(); // تحديث عرض المنتجات
            alert('تم حذف الفئة بنجاح');
        }
    },

    // عرض نموذج تحديث الأسعار المجمع
    showBulkPriceModal() {
        const modal = document.getElementById('bulk-price-modal');

        // تحميل الفئات
        const categorySelect = document.getElementById('bulk-category');
        const data = DB.getData();
        categorySelect.innerHTML = '<option value="">جميع الفئات</option>';

        data.categories.forEach(category => {
            const option = new Option(category.name, category.id);
            categorySelect.appendChild(option);
        });

        modal.classList.remove('hidden');
    },

    // تطبيق تحديث الأسعار المجمع
    applyBulkPriceUpdate() {
        const categoryId = document.getElementById('bulk-category').value;
        const updateType = document.getElementById('bulk-update-type').value;
        const priceType = document.getElementById('bulk-price-type').value;
        const value = parseFloat(document.getElementById('bulk-value').value);

        if (isNaN(value)) {
            alert('يرجى إدخال قيمة صحيحة');
            return;
        }

        const data = DB.getData();
        let products = data.products || [];

        // فلترة حسب الفئة إذا تم تحديدها
        if (categoryId) {
            products = products.filter(p => p.categoryId == categoryId);
        }

        let updatedCount = 0;

        products.forEach(product => {
            let newSalePrice = product.salePrice || 0;
            let newPurchasePrice = product.purchasePrice || 0;

            if (priceType === 'sale' || priceType === 'both') {
                switch (updateType) {
                    case 'percentage':
                        newSalePrice = newSalePrice * (1 + value / 100);
                        break;
                    case 'fixed':
                        newSalePrice = newSalePrice + value;
                        break;
                    case 'set':
                        newSalePrice = value;
                        break;
                }
                product.salePrice = Math.max(0, newSalePrice);
            }

            if (priceType === 'purchase' || priceType === 'both') {
                switch (updateType) {
                    case 'percentage':
                        newPurchasePrice = newPurchasePrice * (1 + value / 100);
                        break;
                    case 'fixed':
                        newPurchasePrice = newPurchasePrice + value;
                        break;
                    case 'set':
                        newPurchasePrice = value;
                        break;
                }
                product.purchasePrice = Math.max(0, newPurchasePrice);
            }

            updatedCount++;
        });

        DB.saveData(data);
        this.loadProducts();
        closeModal('bulk-price-modal');
        alert(`تم تحديث أسعار ${updatedCount} منتج بنجاح`);
    },

    // عرض نموذج تنبيهات المخزون
    showStockAlertsModal() {
        const modal = document.getElementById('stock-alerts-modal');
        this.loadStockAlerts();
        modal.classList.remove('hidden');
    },

    // تحميل تنبيهات المخزون
    loadStockAlerts() {
        const data = DB.getData();
        const products = data.products || [];

        let outOfStock = [];
        let lowStock = [];
        let reorderLevel = [];

        products.forEach(product => {
            if (product.quantity === 0) {
                outOfStock.push(product);
            } else if (product.quantity <= (product.minStock || 5)) {
                lowStock.push(product);
            } else if (product.quantity <= (product.reorderLevel || 5)) {
                reorderLevel.push(product);
            }
        });

        // تحديث الإحصائيات
        document.getElementById('out-of-stock-count').textContent = outOfStock.length;
        document.getElementById('low-stock-count').textContent = lowStock.length;
        document.getElementById('reorder-count').textContent = reorderLevel.length;

        // عرض المنتجات في الجدول
        const tbody = document.getElementById('stock-alerts-table');
        tbody.innerHTML = '';

        const allAlerts = [...outOfStock, ...lowStock, ...reorderLevel];

        if (allAlerts.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center">
                        <i class="fas fa-check-circle" style="color: green; font-size: 24px;"></i>
                        <br>جميع المنتجات في حالة جيدة
                    </td>
                </tr>
            `;
            return;
        }

        allAlerts.forEach(product => {
            const category = data.categories.find(c => c.id === product.categoryId);
            let status = '';
            let statusClass = '';

            if (product.quantity === 0) {
                status = 'نفد المخزون';
                statusClass = 'out';
            } else if (product.quantity <= (product.minStock || 5)) {
                status = 'مخزون منخفض';
                statusClass = 'low';
            } else if (product.quantity <= (product.reorderLevel || 5)) {
                status = 'إعادة طلب';
                statusClass = 'reorder';
            }

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${product.name}</td>
                <td>${category ? category.name : 'غير محدد'}</td>
                <td>${product.quantity || 0}</td>
                <td>${product.minStock || 5}</td>
                <td>${product.reorderLevel || 5}</td>
                <td><span class="stock-status ${statusClass}">${status}</span></td>
                <td>
                    <button onclick="Products.adjustStock(${product.id})" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> إضافة مخزون
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    },

    // عرض نموذج المنتج المفصل
    showDetailedProductModal() {
        const modal = document.getElementById('detailed-product-modal');

        // تحميل الفئات والموردين
        this.loadCategoriesForDetailed();
        this.loadSuppliersForDetailed();

        // مسح النموذج
        this.clearDetailedForm();

        modal.classList.remove('hidden');
    },

    // تحميل الفئات للنموذج المفصل
    loadCategoriesForDetailed() {
        const data = DB.getData();
        const categorySelect = document.getElementById('detailed-product-category');

        categorySelect.innerHTML = '<option value="">اختر الفئة</option>';

        data.categories.forEach(category => {
            const option = new Option(category.name, category.id);
            categorySelect.appendChild(option);
        });
    },

    // تحميل الموردين للنموذج المفصل
    loadSuppliersForDetailed() {
        const data = DB.getData();
        const supplierSelect = document.getElementById('detailed-supplier');

        supplierSelect.innerHTML = '<option value="">اختر المورد</option>';

        if (data.suppliers) {
            data.suppliers.forEach(supplier => {
                const option = new Option(supplier.name, supplier.id);
                supplierSelect.appendChild(option);
            });
        }
    },

    // حفظ المنتج المفصل
    saveDetailedProduct() {
        const form = document.getElementById('detailed-product-form');
        const data = DB.getData();

        // جمع البيانات من النموذج
        const productData = {
            id: DB.getNextId('product'),
            code: document.getElementById('detailed-product-code').value.trim(),
            name: document.getElementById('detailed-product-name').value.trim(),
            nameEn: document.getElementById('detailed-product-name-en').value.trim(),
            description: document.getElementById('detailed-product-description').value.trim(),
            categoryId: parseInt(document.getElementById('detailed-product-category').value) || null,
            subcategory: document.getElementById('detailed-product-subcategory').value.trim(),
            brand: document.getElementById('detailed-product-brand').value.trim(),
            purchasePrice: parseFloat(document.getElementById('detailed-purchase-price').value) || 0,
            salePrice: parseFloat(document.getElementById('detailed-sale-price').value) || 0,
            wholesalePrice: parseFloat(document.getElementById('detailed-wholesale-price').value) || 0,
            taxRate: parseFloat(document.getElementById('detailed-tax-rate').value) || 15,
            discountAllowed: parseFloat(document.getElementById('detailed-discount-allowed').value) || 0,
            unit: document.getElementById('detailed-unit').value,
            quantity: parseFloat(document.getElementById('detailed-initial-quantity').value) || 0,
            minStock: parseFloat(document.getElementById('detailed-min-stock').value) || 5,
            reorderLevel: parseFloat(document.getElementById('detailed-reorder-level').value) || 10,
            maxStock: parseFloat(document.getElementById('detailed-max-stock').value) || 1000,
            location: document.getElementById('detailed-location').value.trim(),
            supplierId: parseInt(document.getElementById('detailed-supplier').value) || null,
            supplierCode: document.getElementById('detailed-supplier-code').value.trim(),
            leadTime: parseInt(document.getElementById('detailed-lead-time').value) || 7,
            barcode: document.getElementById('detailed-barcode').value.trim(),
            weight: parseFloat(document.getElementById('detailed-weight').value) || 0,
            dimensions: document.getElementById('detailed-dimensions').value.trim(),
            expiryDate: document.getElementById('detailed-expiry-date').value,
            warranty: parseInt(document.getElementById('detailed-warranty').value) || 0,
            status: document.getElementById('detailed-status').value,
            notes: document.getElementById('detailed-notes').value.trim(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // التحقق من البيانات المطلوبة
        if (!productData.code) {
            alert('يرجى إدخال كود المنتج');
            document.getElementById('detailed-product-code').focus();
            return;
        }

        if (!productData.name) {
            alert('يرجى إدخال اسم المنتج');
            document.getElementById('detailed-product-name').focus();
            return;
        }

        if (productData.salePrice <= 0) {
            alert('يرجى إدخال سعر بيع صحيح');
            document.getElementById('detailed-sale-price').focus();
            return;
        }

        // التحقق من عدم تكرار الكود
        const existingProduct = data.products.find(p => p.code === productData.code);
        if (existingProduct) {
            alert('كود المنتج موجود مسبقاً، يرجى استخدام كود آخر');
            document.getElementById('detailed-product-code').focus();
            return;
        }

        // إضافة المنتج
        data.products.push(productData);
        DB.saveData(data);

        alert('تم حفظ المنتج بنجاح');
        this.loadProducts();
        this.closeDetailedProductModal();
    },

    // حفظ وإضافة منتج آخر
    saveAndAddAnother() {
        this.saveDetailedProduct();
        // إذا تم الحفظ بنجاح، مسح النموذج للمنتج التالي
        setTimeout(() => {
            this.clearDetailedForm();
            document.getElementById('detailed-product-code').focus();
        }, 100);
    },

    // مسح النموذج المفصل
    clearDetailedForm() {
        const form = document.getElementById('detailed-product-form');
        form.reset();

        // تعيين القيم الافتراضية
        document.getElementById('detailed-unit').value = 'قطعة';
        document.getElementById('detailed-initial-quantity').value = 0;
        document.getElementById('detailed-min-stock').value = 5;
        document.getElementById('detailed-reorder-level').value = 10;
        document.getElementById('detailed-max-stock').value = 1000;
        document.getElementById('detailed-tax-rate').value = 15;
        document.getElementById('detailed-discount-allowed').value = 0;
        document.getElementById('detailed-lead-time').value = 7;
        document.getElementById('detailed-warranty').value = 0;
        document.getElementById('detailed-status').value = 'active';
        document.getElementById('detailed-profit-margin').value = '';
    },

    // حساب هامش الربح
    calculateProfitMargin() {
        const purchasePrice = parseFloat(document.getElementById('detailed-purchase-price').value) || 0;
        const salePrice = parseFloat(document.getElementById('detailed-sale-price').value) || 0;

        if (purchasePrice > 0 && salePrice > 0) {
            const margin = ((salePrice - purchasePrice) / purchasePrice * 100).toFixed(2);
            document.getElementById('detailed-profit-margin').value = margin;
        } else {
            document.getElementById('detailed-profit-margin').value = '';
        }
    },

    // توليد كود المنتج تلقائياً
    generateProductCode() {
        const data = DB.getData();
        const nextId = data.lastIds.product + 1;
        const code = `PRD${nextId.toString().padStart(6, '0')}`;
        document.getElementById('detailed-product-code').value = code;
    },

    // توليد باركود
    generateBarcode() {
        const timestamp = Date.now().toString();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        const barcode = `${timestamp.slice(-8)}${random}`;
        document.getElementById('detailed-barcode').value = barcode;
    },

    // وظائف إغلاق النوافذ
    closeBulkPriceModal() {
        document.getElementById('bulk-price-modal').classList.add('hidden');
    },

    closeStockAlertsModal() {
        document.getElementById('stock-alerts-modal').classList.add('hidden');
    },

    closeDetailedProductModal() {
        document.getElementById('detailed-product-modal').classList.add('hidden');
    },

    // تنسيق العملة
    formatCurrency(amount) {
        const data = DB.getData();
        const currency = data.settings.currency || 'ريال';
        return `${amount.toFixed(2)} ${currency}`;
    }
};