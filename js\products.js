/**
 * وحدة إدارة المنتجات
 */
const Products = {
    init(container) {
        this.container = container;
        this.render();
        this.loadProducts();
        this.setupEventListeners();
    },
    
    render() {
        this.container.innerHTML = `
            <div class="page-header">
                <h2 class="page-title">إدارة المنتجات</h2>
                <button id="add-product-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة منتج جديد
                </button>
            </div>
            
            <div class="search-container">
                <input type="text" id="product-search" class="form-control" placeholder="بحث عن منتج...">
            </div>
            
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>الباركود</th>
                            <th>اسم المنتج</th>
                            <th>الفئة</th>
                            <th>سعر الشراء</th>
                            <th>سعر البيع</th>
                            <th>الكمية</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="products-list">
                        <tr>
                            <td colspan="8">جاري تحميل البيانات...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- نموذج إضافة/تعديل منتج -->
            <div id="product-modal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="modal-title">إضافة منتج جديد</h3>
                        <button id="close-modal" class="btn-close">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="product-form">
                            <input type="hidden" id="product-id">
                            
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="product-barcode">الباركود</label>
                                        <input type="text" id="product-barcode" class="form-control" placeholder="أدخل الباركود">
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="product-name">اسم المنتج</label>
                                        <input type="text" id="product-name" class="form-control" placeholder="أدخل اسم المنتج" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="product-category">الفئة</label>
                                        <select id="product-category" class="form-control">
                                            <!-- سيتم تحميل الفئات هنا -->
                                        </select>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="product-unit">وحدة القياس</label>
                                        <select id="product-unit" class="form-control">
                                            <option value="piece">قطعة</option>
                                            <option value="kg">كيلوجرام</option>
                                            <option value="liter">لتر</option>
                                            <option value="meter">متر</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="product-cost">سعر الشراء</label>
                                        <input type="number" id="product-cost" class="form-control" placeholder="0.00" step="0.01" min="0" required>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="product-price">سعر البيع</label>
                                        <input type="number" id="product-price" class="form-control" placeholder="0.00" step="0.01" min="0" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="product-quantity">الكمية</label>
                                        <input type="number" id="product-quantity" class="form-control" placeholder="0" min="0" required>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="product-min-quantity">الحد الأدنى للكمية</label>
                                        <input type="number" id="product-min-quantity" class="form-control" placeholder="0" min="0">
                                    </div