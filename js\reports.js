/**
 * التقارير والإحصائيات
 */
const Reports = {
    // عرض صفحة التقارير
    show() {
        const content = document.getElementById('page-content');
        content.innerHTML = `
            <div class="page-header">
                <h2><i class="fas fa-chart-bar"></i> التقارير والإحصائيات</h2>
                <button id="export-reports-btn" class="btn btn-secondary">
                    <i class="fas fa-download"></i> تصدير التقارير
                </button>
            </div>

            <div class="reports-grid">
                <!-- تقارير المبيعات -->
                <div class="card report-card">
                    <h3><i class="fas fa-shopping-cart"></i> تقارير المبيعات</h3>
                    <div class="report-buttons">
                        <button onclick="Reports.showSalesReport('daily')" class="btn btn-primary">
                            <i class="fas fa-calendar-day"></i> مبيعات اليوم
                        </button>
                        <button onclick="Reports.showSalesReport('weekly')" class="btn btn-primary">
                            <i class="fas fa-calendar-week"></i> مبيعات الأسبوع
                        </button>
                        <button onclick="Reports.showSalesReport('monthly')" class="btn btn-primary">
                            <i class="fas fa-calendar-alt"></i> مبيعات الشهر
                        </button>
                        <button onclick="Reports.showSalesReport('yearly')" class="btn btn-primary">
                            <i class="fas fa-calendar"></i> مبيعات السنة
                        </button>
                    </div>
                </div>

                <!-- تقارير المخزون -->
                <div class="card report-card">
                    <h3><i class="fas fa-warehouse"></i> تقارير المخزون</h3>
                    <div class="report-buttons">
                        <button onclick="Reports.showInventoryReport()" class="btn btn-success">
                            <i class="fas fa-boxes"></i> تقرير المخزون الحالي
                        </button>
                        <button onclick="Reports.showLowStockReport()" class="btn btn-warning">
                            <i class="fas fa-exclamation-triangle"></i> المنتجات منخفضة المخزون
                        </button>
                        <button onclick="Reports.showStockMovements()" class="btn btn-info">
                            <i class="fas fa-exchange-alt"></i> حركات المخزون
                        </button>
                    </div>
                </div>

                <!-- تقارير العملاء -->
                <div class="card report-card">
                    <h3><i class="fas fa-users"></i> تقارير العملاء</h3>
                    <div class="report-buttons">
                        <button onclick="Reports.showCustomersReport()" class="btn btn-info">
                            <i class="fas fa-list"></i> قائمة العملاء
                        </button>
                        <button onclick="Reports.showCustomersDebt()" class="btn btn-warning">
                            <i class="fas fa-money-bill-wave"></i> ديون العملاء
                        </button>
                        <button onclick="Reports.showTopCustomers()" class="btn btn-success">
                            <i class="fas fa-star"></i> أفضل العملاء
                        </button>
                    </div>
                </div>

                <!-- تقارير المنتجات -->
                <div class="card report-card">
                    <h3><i class="fas fa-box"></i> تقارير المنتجات</h3>
                    <div class="report-buttons">
                        <button onclick="Reports.showTopProducts()" class="btn btn-success">
                            <i class="fas fa-trophy"></i> أفضل المنتجات مبيعاً
                        </button>
                        <button onclick="Reports.showProductsProfit()" class="btn btn-primary">
                            <i class="fas fa-chart-line"></i> ربحية المنتجات
                        </button>
                        <button onclick="Reports.showProductsCategories()" class="btn btn-info">
                            <i class="fas fa-tags"></i> تقرير الفئات
                        </button>
                    </div>
                </div>

                <!-- التقارير المالية -->
                <div class="card report-card">
                    <h3><i class="fas fa-calculator"></i> التقارير المالية</h3>
                    <div class="report-buttons">
                        <button onclick="Reports.showProfitLoss()" class="btn btn-primary">
                            <i class="fas fa-chart-pie"></i> قائمة الدخل
                        </button>
                        <button onclick="Reports.showCashFlow()" class="btn btn-success">
                            <i class="fas fa-money-check-alt"></i> التدفق النقدي
                        </button>
                        <button onclick="Reports.showTaxReport()" class="btn btn-warning">
                            <i class="fas fa-receipt"></i> تقرير الضرائب
                        </button>
                    </div>
                </div>

                <!-- تقارير مخصصة -->
                <div class="card report-card">
                    <h3><i class="fas fa-filter"></i> تقارير مخصصة</h3>
                    <div class="custom-report-form">
                        <div class="form-group">
                            <label for="report-type">نوع التقرير</label>
                            <select id="report-type" class="form-control">
                                <option value="sales">المبيعات</option>
                                <option value="purchases">المشتريات</option>
                                <option value="customers">العملاء</option>
                                <option value="products">المنتجات</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="date-from">من تاريخ</label>
                            <input type="date" id="date-from" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="date-to">إلى تاريخ</label>
                            <input type="date" id="date-to" class="form-control">
                        </div>
                        <button onclick="Reports.showCustomReport()" class="btn btn-primary">
                            <i class="fas fa-search"></i> إنشاء التقرير
                        </button>
                    </div>
                </div>
            </div>

            <!-- منطقة عرض التقارير -->
            <div id="report-display" class="card hidden">
                <div class="report-header">
                    <h3 id="report-title">عنوان التقرير</h3>
                    <div class="report-actions">
                        <button onclick="Reports.printReport()" class="btn btn-secondary">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <button onclick="Reports.exportReport()" class="btn btn-success">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                        <button onclick="Reports.closeReport()" class="btn btn-danger">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                </div>
                <div id="report-content"></div>
            </div>
        `;

        this.attachEvents();
        this.setDefaultDates();
        this.loadQuickStats();
    },

    // تحميل الإحصائيات السريعة
    loadQuickStats() {
        const data = DB.getData();

        // حساب إجمالي المبيعات
        const totalSales = (data.sales || []).reduce((sum, sale) => sum + (sale.grandTotal || 0), 0);

        // حساب إجمالي المشتريات
        const totalPurchases = (data.purchases || []).reduce((sum, purchase) => sum + (purchase.total || 0), 0);

        // حساب إجمالي الأرباح (تقديري)
        const totalProfit = totalSales - totalPurchases;

        // عدد الأصناف
        const totalProducts = (data.products || []).length;

        // عدد العملاء
        const totalCustomers = (data.customers || []).length;

        // عدد الموردين
        const totalSuppliers = (data.suppliers || []).length;

        // تحديث العناصر في الصفحة إذا كانت موجودة
        const elements = {
            'total-sales': this.formatCurrency(totalSales),
            'total-purchases': this.formatCurrency(totalPurchases),
            'total-profit': this.formatCurrency(totalProfit),
            'total-products': totalProducts,
            'total-customers': totalCustomers,
            'total-suppliers': totalSuppliers
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    },

    // تنسيق العملة
    formatCurrency(amount) {
        return App.formatCurrency(amount);
    },

    // إضافة الأحداث
    attachEvents() {
        document.getElementById('export-reports-btn').addEventListener('click', () => {
            this.exportAllReports();
        });
    },

    // تعيين التواريخ الافتراضية
    setDefaultDates() {
        const today = new Date();
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

        document.getElementById('date-from').value = firstDayOfMonth.toISOString().split('T')[0];
        document.getElementById('date-to').value = today.toISOString().split('T')[0];
    },

    // عرض تقرير المبيعات
    showSalesReport(period) {
        const data = DB.getData();
        const sales = data.sales || [];

        let filteredSales = [];
        let title = '';
        const now = new Date();

        switch (period) {
            case 'daily':
                const today = now.toISOString().split('T')[0];
                filteredSales = sales.filter(sale => sale.date.split('T')[0] === today);
                title = `تقرير مبيعات اليوم - ${now.toLocaleDateString('ar-SA')}`;
                break;

            case 'weekly':
                const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
                filteredSales = sales.filter(sale => new Date(sale.date) >= weekStart);
                title = `تقرير مبيعات الأسبوع`;
                break;

            case 'monthly':
                const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
                filteredSales = sales.filter(sale => new Date(sale.date) >= monthStart);
                title = `تقرير مبيعات الشهر - ${now.toLocaleDateString('ar-SA', { month: 'long', year: 'numeric' })}`;
                break;

            case 'yearly':
                const yearStart = new Date(now.getFullYear(), 0, 1);
                filteredSales = sales.filter(sale => new Date(sale.date) >= yearStart);
                title = `تقرير مبيعات السنة - ${now.getFullYear()}`;
                break;
        }

        this.displaySalesReport(filteredSales, title, data);
    },

    // عرض تقرير المبيعات
    displaySalesReport(sales, title, data) {
        const totalSales = sales.reduce((sum, sale) => sum + sale.grandTotal, 0);
        const totalTax = sales.reduce((sum, sale) => sum + sale.tax, 0);
        const totalItems = sales.reduce((sum, sale) => sum + sale.items.length, 0);

        const content = `
            <div class="report-summary">
                <div class="summary-grid">
                    <div class="summary-item">
                        <h4>عدد الفواتير</h4>
                        <span class="summary-value">${sales.length}</span>
                    </div>
                    <div class="summary-item">
                        <h4>إجمالي المبيعات</h4>
                        <span class="summary-value">${this.formatCurrency(totalSales)}</span>
                    </div>
                    <div class="summary-item">
                        <h4>إجمالي الضرائب</h4>
                        <span class="summary-value">${this.formatCurrency(totalTax)}</span>
                    </div>
                    <div class="summary-item">
                        <h4>عدد الأصناف</h4>
                        <span class="summary-value">${totalItems}</span>
                    </div>
                </div>
            </div>

            <div class="report-table">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>المجموع</th>
                            <th>الضريبة</th>
                            <th>الإجمالي</th>
                            <th>طريقة الدفع</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${sales.map(sale => {
                            const customer = data.customers.find(c => c.id === sale.customerId);
                            const paymentText = sale.paymentMethod === 'cash' ? 'نقد' : 'آجل';
                            return `
                                <tr>
                                    <td>#${sale.id}</td>
                                    <td>${customer ? customer.name : 'غير محدد'}</td>
                                    <td>${new Date(sale.date).toLocaleDateString('ar-SA')}</td>
                                    <td>${this.formatCurrency(sale.total)}</td>
                                    <td>${this.formatCurrency(sale.tax)}</td>
                                    <td>${this.formatCurrency(sale.grandTotal)}</td>
                                    <td>${paymentText}</td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;

        this.showReport(title, content);
    },

    // عرض تقرير المخزون
    showInventoryReport() {
        const data = DB.getData();
        const products = data.products || [];

        const totalValue = products.reduce((sum, product) => sum + (product.quantity * (product.cost || product.price)), 0);
        const totalQuantity = products.reduce((sum, product) => sum + product.quantity, 0);

        const content = `
            <div class="report-summary">
                <div class="summary-grid">
                    <div class="summary-item">
                        <h4>عدد المنتجات</h4>
                        <span class="summary-value">${products.length}</span>
                    </div>
                    <div class="summary-item">
                        <h4>إجمالي الكمية</h4>
                        <span class="summary-value">${totalQuantity}</span>
                    </div>
                    <div class="summary-item">
                        <h4>قيمة المخزون</h4>
                        <span class="summary-value">${this.formatCurrency(totalValue)}</span>
                    </div>
                </div>
            </div>

            <div class="report-table">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>اسم المنتج</th>
                            <th>الفئة</th>
                            <th>الكمية</th>
                            <th>سعر التكلفة</th>
                            <th>سعر البيع</th>
                            <th>قيمة المخزون</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${products.map(product => {
                            const category = data.categories.find(c => c.id === product.categoryId);
                            const stockValue = product.quantity * (product.cost || product.price);
                            let status = 'جيد';
                            let statusClass = 'good';

                            if (product.quantity === 0) {
                                status = 'نفد';
                                statusClass = 'out';
                            } else if (product.quantity <= (product.minStock || 5)) {
                                status = 'منخفض';
                                statusClass = 'low';
                            }

                            return `
                                <tr>
                                    <td>${product.name}</td>
                                    <td>${category ? category.name : 'غير محدد'}</td>
                                    <td>${product.quantity}</td>
                                    <td>${this.formatCurrency(product.cost || 0)}</td>
                                    <td>${this.formatCurrency(product.price)}</td>
                                    <td>${this.formatCurrency(stockValue)}</td>
                                    <td><span class="stock-status ${statusClass}">${status}</span></td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;

        this.showReport('تقرير المخزون الحالي', content);
    },

    // عرض تقرير المنتجات منخفضة المخزون
    showLowStockReport() {
        const data = DB.getData();
        const products = data.products || [];
        const lowStockProducts = products.filter(product =>
            product.quantity <= (product.minStock || 5)
        );

        const content = `
            <div class="report-summary">
                <div class="summary-grid">
                    <div class="summary-item">
                        <h4>عدد المنتجات منخفضة المخزون</h4>
                        <span class="summary-value">${lowStockProducts.length}</span>
                    </div>
                </div>
            </div>

            <div class="report-table">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>اسم المنتج</th>
                            <th>الكمية الحالية</th>
                            <th>الحد الأدنى</th>
                            <th>الكمية المطلوبة</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${lowStockProducts.map(product => {
                            const minStock = product.minStock || 5;
                            const needed = Math.max(0, minStock - product.quantity);
                            let status = product.quantity === 0 ? 'نفد' : 'منخفض';
                            let statusClass = product.quantity === 0 ? 'out' : 'low';

                            return `
                                <tr>
                                    <td>${product.name}</td>
                                    <td>${product.quantity}</td>
                                    <td>${minStock}</td>
                                    <td>${needed}</td>
                                    <td><span class="stock-status ${statusClass}">${status}</span></td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;

        this.showReport('تقرير المنتجات منخفضة المخزون', content);
    },

    // عرض تقرير العملاء
    showCustomersReport() {
        const data = DB.getData();
        const customers = data.customers.filter(c => !c.isDefault) || [];

        const content = `
            <div class="report-summary">
                <div class="summary-grid">
                    <div class="summary-item">
                        <h4>عدد العملاء</h4>
                        <span class="summary-value">${customers.length}</span>
                    </div>
                </div>
            </div>

            <div class="report-table">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>اسم العميل</th>
                            <th>الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>الرصيد</th>
                            <th>تاريخ التسجيل</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${customers.map(customer => `
                            <tr>
                                <td>${customer.name}</td>
                                <td>${customer.phone || '-'}</td>
                                <td>${customer.email || '-'}</td>
                                <td class="balance ${customer.balance > 0 ? 'positive' : customer.balance < 0 ? 'negative' : ''}">${this.formatCurrency(customer.balance)}</td>
                                <td>${new Date(customer.createdAt).toLocaleDateString('ar-SA')}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        this.showReport('تقرير العملاء', content);
    },

    // عرض تقرير ديون العملاء
    showCustomersDebt() {
        const data = DB.getData();
        const customers = data.customers.filter(c => !c.isDefault && c.balance > 0) || [];
        const totalDebt = customers.reduce((sum, customer) => sum + customer.balance, 0);

        const content = `
            <div class="report-summary">
                <div class="summary-grid">
                    <div class="summary-item">
                        <h4>عدد العملاء المدينين</h4>
                        <span class="summary-value">${customers.length}</span>
                    </div>
                    <div class="summary-item">
                        <h4>إجمالي الديون</h4>
                        <span class="summary-value">${this.formatCurrency(totalDebt)}</span>
                    </div>
                </div>
            </div>

            <div class="report-table">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>اسم العميل</th>
                            <th>الهاتف</th>
                            <th>مبلغ الدين</th>
                            <th>آخر معاملة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${customers.map(customer => {
                            const lastSale = data.sales
                                .filter(s => s.customerId === customer.id)
                                .sort((a, b) => new Date(b.date) - new Date(a.date))[0];

                            return `
                                <tr>
                                    <td>${customer.name}</td>
                                    <td>${customer.phone || '-'}</td>
                                    <td class="balance positive">${this.formatCurrency(customer.balance)}</td>
                                    <td>${lastSale ? new Date(lastSale.date).toLocaleDateString('ar-SA') : '-'}</td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;

        this.showReport('تقرير ديون العملاء', content);
    },

    // عرض أفضل العملاء
    showTopCustomers() {
        const data = DB.getData();
        const customerSales = {};

        // حساب مبيعات كل عميل
        data.sales.forEach(sale => {
            if (customerSales[sale.customerId]) {
                customerSales[sale.customerId].totalSales += sale.grandTotal;
                customerSales[sale.customerId].salesCount += 1;
            } else {
                const customer = data.customers.find(c => c.id === sale.customerId);
                if (customer) {
                    customerSales[sale.customerId] = {
                        customerId: sale.customerId,
                        customerName: customer.name,
                        customerPhone: customer.phone || '-',
                        totalSales: sale.grandTotal,
                        salesCount: 1
                    };
                }
            }
        });

        const topCustomers = Object.values(customerSales)
            .sort((a, b) => b.totalSales - a.totalSales)
            .slice(0, 20);

        const totalSalesAmount = topCustomers.reduce((sum, customer) => sum + customer.totalSales, 0);

        const content = `
            <div class="report-summary">
                <div class="summary-grid">
                    <div class="summary-item">
                        <h4>عدد العملاء النشطين</h4>
                        <span class="summary-value">${topCustomers.length}</span>
                    </div>
                    <div class="summary-item">
                        <h4>إجمالي مبيعات أفضل العملاء</h4>
                        <span class="summary-value">${this.formatCurrency(totalSalesAmount)}</span>
                    </div>
                </div>
            </div>

            <div class="report-table">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>الترتيب</th>
                            <th>اسم العميل</th>
                            <th>رقم الهاتف</th>
                            <th>عدد الفواتير</th>
                            <th>إجمالي المبيعات</th>
                            <th>متوسط الفاتورة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${topCustomers.map((customer, index) => {
                            const averageInvoice = customer.totalSales / customer.salesCount;
                            return `
                                <tr>
                                    <td>${index + 1}</td>
                                    <td>${customer.customerName}</td>
                                    <td>${customer.customerPhone}</td>
                                    <td>${customer.salesCount}</td>
                                    <td>${this.formatCurrency(customer.totalSales)}</td>
                                    <td>${this.formatCurrency(averageInvoice)}</td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;

        this.showReport('أفضل العملاء', content);
    },

    // عرض أفضل المنتجات مبيعاً
    showTopProducts() {
        const data = DB.getData();
        const productSales = {};

        data.sales.forEach(sale => {
            sale.items.forEach(item => {
                if (productSales[item.productId]) {
                    productSales[item.productId].quantity += item.quantity;
                    productSales[item.productId].revenue += item.total;
                } else {
                    productSales[item.productId] = {
                        productId: item.productId,
                        productName: item.productName,
                        quantity: item.quantity,
                        revenue: item.total
                    };
                }
            });
        });

        const topProducts = Object.values(productSales)
            .sort((a, b) => b.quantity - a.quantity)
            .slice(0, 20);

        const content = `
            <div class="report-summary">
                <div class="summary-grid">
                    <div class="summary-item">
                        <h4>عدد المنتجات المباعة</h4>
                        <span class="summary-value">${Object.keys(productSales).length}</span>
                    </div>
                </div>
            </div>

            <div class="report-table">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>الترتيب</th>
                            <th>اسم المنتج</th>
                            <th>الكمية المباعة</th>
                            <th>إجمالي الإيرادات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${topProducts.map((product, index) => `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${product.productName}</td>
                                <td>${product.quantity}</td>
                                <td>${this.formatCurrency(product.revenue)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        this.showReport('أفضل المنتجات مبيعاً', content);
    },

    // عرض التقرير
    showReport(title, content) {
        document.getElementById('report-title').textContent = title;
        document.getElementById('report-content').innerHTML = content;
        document.getElementById('report-display').classList.remove('hidden');

        // التمرير إلى التقرير
        document.getElementById('report-display').scrollIntoView({ behavior: 'smooth' });
    },

    // إغلاق التقرير
    closeReport() {
        document.getElementById('report-display').classList.add('hidden');
    },

    // طباعة التقرير
    printReport() {
        const reportContent = document.getElementById('report-display').innerHTML;
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>طباعة التقرير</title>
                    <style>
                        body { font-family: 'Tajawal', Arial, sans-serif; direction: rtl; }
                        .data-table { width: 100%; border-collapse: collapse; }
                        .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        .data-table th { background-color: #f5f5f5; }
                        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
                        .summary-item { text-align: center; padding: 15px; border: 1px solid #ddd; }
                        .summary-value { font-size: 24px; font-weight: bold; color: #3498db; }
                        .stock-status.good { color: #27ae60; }
                        .stock-status.low { color: #f39c12; }
                        .stock-status.out { color: #e74c3c; }
                        .balance.positive { color: #27ae60; }
                        .balance.negative { color: #e74c3c; }
                    </style>
                </head>
                <body>${reportContent}</body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    },

    // تصدير التقرير
    exportReport() {
        alert('سيتم إضافة ميزة التصدير في التحديث القادم');
    },

    // تصدير جميع التقارير
    exportAllReports() {
        alert('سيتم إضافة ميزة تصدير جميع التقارير في التحديث القادم');
    },

    // كشف حساب العميل
    customerStatement(customerId) {
        const data = DB.getData();
        const customer = data.customers.find(c => c.id === customerId);
        if (!customer) return;

        const sales = data.sales.filter(s => s.customerId === customerId);
        const payments = data.payments ? data.payments.filter(p => p.customerId === customerId) : [];

        const content = `
            <div class="customer-statement">
                <h4>كشف حساب العميل: ${customer.name}</h4>
                <div class="customer-info">
                    <p><strong>الهاتف:</strong> ${customer.phone || '-'}</p>
                    <p><strong>البريد:</strong> ${customer.email || '-'}</p>
                    <p><strong>الرصيد الحالي:</strong> ${this.formatCurrency(customer.balance)}</p>
                </div>

                <div class="transactions-table">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>الرقم</th>
                                <th>المبلغ</th>
                                <th>الرصيد</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${this.generateStatementRows(sales, payments)}
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        this.showReport(`كشف حساب العميل - ${customer.name}`, content);
    },

    // كشف حساب المورد
    supplierStatement(supplierId) {
        const data = DB.getData();
        const supplier = data.suppliers.find(s => s.id === supplierId);
        if (!supplier) return;

        const purchases = data.purchases.filter(p => p.supplierId === supplierId);
        const payments = data.supplierPayments ? data.supplierPayments.filter(p => p.supplierId === supplierId) : [];

        const content = `
            <div class="supplier-statement">
                <h4>كشف حساب المورد: ${supplier.name}</h4>
                <div class="supplier-info">
                    <p><strong>الهاتف:</strong> ${supplier.phone || '-'}</p>
                    <p><strong>البريد:</strong> ${supplier.email || '-'}</p>
                    <p><strong>الرصيد الحالي:</strong> ${this.formatCurrency(supplier.balance)}</p>
                </div>

                <div class="transactions-table">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>الرقم</th>
                                <th>المبلغ</th>
                                <th>الرصيد</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${this.generateSupplierStatementRows(purchases, payments)}
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        this.showReport(`كشف حساب المورد - ${supplier.name}`, content);
    },

    // إنشاء صفوف كشف حساب المورد
    generateSupplierStatementRows(purchases, payments) {
        const transactions = [];

        purchases.forEach(purchase => {
            transactions.push({
                date: purchase.date,
                type: 'مشتريات',
                number: purchase.id,
                amount: purchase.total,
                isDebit: true
            });
        });

        payments.forEach(payment => {
            transactions.push({
                date: payment.date,
                type: 'دفعة',
                number: payment.id,
                amount: payment.amount,
                isDebit: false
            });
        });

        transactions.sort((a, b) => new Date(a.date) - new Date(b.date));

        let balance = 0;
        return transactions.map(transaction => {
            if (transaction.isDebit) {
                balance += transaction.amount;
            } else {
                balance -= transaction.amount;
            }

            return `
                <tr>
                    <td>${new Date(transaction.date).toLocaleDateString('ar-SA')}</td>
                    <td>${transaction.type}</td>
                    <td>#${transaction.number}</td>
                    <td class="${transaction.isDebit ? 'debit' : 'credit'}">${this.formatCurrency(transaction.amount)}</td>
                    <td>${this.formatCurrency(balance)}</td>
                </tr>
            `;
        }).join('');
    },

    // إنشاء صفوف كشف الحساب
    generateStatementRows(sales, payments) {
        const transactions = [];

        sales.forEach(sale => {
            transactions.push({
                date: sale.date,
                type: 'مبيعات',
                number: sale.id,
                amount: sale.grandTotal,
                isDebit: true
            });
        });

        payments.forEach(payment => {
            transactions.push({
                date: payment.date,
                type: 'دفعة',
                number: payment.id,
                amount: payment.amount,
                isDebit: false
            });
        });

        transactions.sort((a, b) => new Date(a.date) - new Date(b.date));

        let balance = 0;
        return transactions.map(transaction => {
            if (transaction.isDebit) {
                balance += transaction.amount;
            } else {
                balance -= transaction.amount;
            }

            return `
                <tr>
                    <td>${new Date(transaction.date).toLocaleDateString('ar-SA')}</td>
                    <td>${transaction.type}</td>
                    <td>#${transaction.number}</td>
                    <td class="${transaction.isDebit ? 'debit' : 'credit'}">${this.formatCurrency(transaction.amount)}</td>
                    <td>${this.formatCurrency(balance)}</td>
                </tr>
            `;
        }).join('');
    },

    // تنسيق العملة (نسخة احتياطية)
    formatCurrencyBackup(amount) {
        return App.formatCurrency(amount);
    }
};
