/**
 * وحدة التقارير
 */
const Reports = {
    init(container) {
        this.container = container;
        this.render();
        this.setupEventListeners();
    },
    
    render() {
        this.container.innerHTML = `
            <h2 class="page-title">التقارير</h2>
            
            <div class="report-filters card">
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="report-type">نوع التقرير</label>
                            <select id="report-type" class="form-control">
                                <option value="sales">تقرير المبيعات</option>
                                <option value="purchases">تقرير المشتريات</option>
                                <option value="inventory">تقرير المخزون</option>
                                <option value="customers">تقرير العملاء</option>
                                <option value="suppliers">تقرير الموردين</option>
                                <option value="profit">تقرير الأرباح والخسائر</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="date-range">الفترة الزمنية</label>
                            <select id="date-range" class="form-control">
                                <option value="today">اليوم</option>
                                <option value="yesterday">الأمس</option>
                                <option value="this-week">هذا الأسبوع</option>
                                <option value="this-month">هذا الشهر</option>
                                <option value="last-month">الشهر الماضي</option>
                                <option value="this-year">هذه السنة</option>
                                <option value="custom">فترة مخصصة</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div id="custom-date-range" class="form-row hidden">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="start-date">من تاريخ</label>
                            <input type="date" id="start-date" class="form-control">
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="end-date">إلى تاريخ</label>
                            <input type="date" id="end-date" class="form-control">
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <button id="generate-report" class="btn btn-primary">إنشاء التقرير</button>
                </div>
            </div>
            
            <div id="report-result" class="card">
                <div class="report-header">
                    <h3 id="report-title">تقرير المبيعات</h3>
                    <div class="report-actions">
                        <button id="print-report" class="btn btn-primary">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <button id="export-report-excel" class="btn btn-primary">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                        <button id="export-report-pdf" class="btn btn-primary">
                            <i class="fas fa-file-pdf"></i> تصدير PDF
                        </button>
                    </div>
                </div>
                
                <div id="report-content">
                    <p>اختر نوع التقرير والفترة الزمنية ثم انقر على "إنشاء التقرير"</p>
                </div>
            </div>
        `;
    },
    
    setupEventListeners() {
        // إظهار/إخفاء حقول التاريخ المخصص
        document.getElementById('date-range').addEventListener('change', (e) => {
            const customDateRange = document.getElementById('custom-date-range');
            if (e.target.value === 'custom') {
                customDateRange.classList.remove('hidden');
            } else {
                customDateRange.classList.add('hidden');
            }
        });
        
        // إنشاء التقرير
        document.getElementById('generate-report').addEventListener('click', () => {
            const reportType = document.getElementById('report-type').value;
            const dateRange = document.getElementById('date-range').value;
            
            let startDate, endDate;
            
            if (dateRange === 'custom') {
                startDate = document.getElementById('start-date').value;
                endDate = document.getElementById('end-date').value;
                
                if (!startDate || !endDate) {
                    alert('الرجاء تحديد تاريخ البداية والنهاية');
                    return;
                }
            } else {
                // حساب الفترة الزمنية بناءً على الاختيار
                const dates = this.calculateDateRange(dateRange);
                startDate = dates.startDate;
                endDate = dates.endDate;
            }
            
            // إنشاء التقرير المطلوب
            this.generateReport(reportType, startDate, endDate);
        });
        
        // طباعة التقرير
        document.getElementById('print-report').addEventListener('click', () => {
            window.print();
        });
        
        // تصدير التقرير بصيغة Excel
        document.getElementById('export-report-excel').addEventListener('click', () => {
            const reportType = document.getElementById('report-type').value;
            alert(`سيتم تصدير تقرير ${reportType} بصيغة Excel في الإصدار القادم`);
        });
        
        // تصدير التقرير بصيغة PDF
        document.getElementById('export-report-pdf').addEventListener('click', () => {
            const reportType = document.getElementById('report-type').value;
            alert(`سيتم تصدير تقرير ${reportType} بصيغة PDF في الإصدار القادم`);
        });
    },
    
    calculateDateRange(range) {
        const today = new Date();
        let startDate = new Date();
        let endDate = new Date();
        
        switch (range) {
            case 'today':
                // اليوم الحالي
                break;
                
            case 'yesterday':
                // الأمس
                startDate.setDate(today.getDate() - 1);
                endDate.setDate(today.getDate() - 1);
                break;
                
            case 'this-week':
                // هذا الأسبوع (من الأحد إلى السبت)
                const dayOfWeek = today.getDay(); // 0 = الأحد، 6 = السبت
                startDate.setDate(today.getDate() - dayOfWeek); // الأحد
                endDate.setDate(startDate.getDate() + 6); // السبت
                break;
                
            case 'this-month':
                // هذا الشهر
                startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                break;
                
            case 'last-month':
                // الشهر الماضي
                startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                endDate = new Date(today.getFullYear(), today.getMonth(), 0);
                break;
                
            case 'this-year':
                // هذه السنة
                startDate = new Date(today.getFullYear(), 0, 1);
                endDate = new Date(today.getFullYear(), 11, 31);
                break;
        }
        
        // تنسيق التواريخ بصيغة YYYY-MM-DD
        const formatDate = (date) => {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        };
        
        return {
            startDate: formatDate(startDate),
            endDate: formatDate(endDate)
        };
    },
    
    generateReport(reportType, startDate, endDate) {
        const data = DB.getData();
        const reportTitle = document.getElementById('report-title');
        const reportContent = document.getElementById('report-content');
        
        // تحديث عنوان التقرير
        let title;
        switch (reportType) {
            case 'sales': title = 'تقرير المبيعات'; break;
            case 'purchases': title = 'تقرير المشتريات'; break;
            case 'inventory': title = 'تقرير المخزون'; break;
            case 'customers': title = 'تقرير العملاء'; break;
            case 'suppliers': title = 'تقرير الموردين'; break;
            case 'profit': title = 'تقرير الأرباح والخسائر'; break;
        }
        
        reportTitle.textContent = `${title} (${startDate} إلى ${endDate})`;
        
        // إنشاء محتوى التقرير بناءً على النوع
        let content = '';
        
        switch (reportType) {
            case 'sales':
                content = this.generateSalesReport(data, startDate, endDate);
                break;
                
