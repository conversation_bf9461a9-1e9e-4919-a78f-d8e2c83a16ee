/**
 * ملف التهيئة الرئيسي للتطبيق
 */

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing app...');
    
    // التحقق من وجود جميع الوحدات المطلوبة
    const requiredModules = ['DB', 'App', 'Dashboard', 'Products', 'Sales', 'Customers', 'Suppliers', 'Purchases', 'Reports', 'Settings'];
    const missingModules = [];
    
    requiredModules.forEach(module => {
        if (typeof window[module] === 'undefined') {
            missingModules.push(module);
        }
    });
    
    if (missingModules.length > 0) {
        console.error('Missing modules:', missingModules);
        document.body.innerHTML = `
            <div style="padding: 20px; text-align: center; font-family: Arial, sans-serif;">
                <h2>خطأ في تحميل التطبيق</h2>
                <p>لم يتم تحميل الوحدات التالية:</p>
                <ul style="list-style: none; color: red;">
                    ${missingModules.map(module => `<li>${module}</li>`).join('')}
                </ul>
                <p>يرجى التحقق من ملفات JavaScript والمحاولة مرة أخرى.</p>
                <button onclick="location.reload()" style="padding: 10px 20px; font-size: 16px;">إعادة تحميل</button>
            </div>
        `;
        return;
    }
    
    try {
        // تهيئة قاعدة البيانات أولاً
        console.log('Initializing database...');
        DB.init();
        
        // تهيئة التطبيق
        console.log('Initializing app...');
        App.init();
        
        console.log('App initialized successfully');
    } catch (error) {
        console.error('Error initializing app:', error);
        document.body.innerHTML = `
            <div style="padding: 20px; text-align: center; font-family: Arial, sans-serif;">
                <h2>خطأ في تهيئة التطبيق</h2>
                <p>حدث خطأ أثناء تهيئة التطبيق:</p>
                <p style="color: red; font-family: monospace;">${error.message}</p>
                <button onclick="location.reload()" style="padding: 10px 20px; font-size: 16px;">إعادة تحميل</button>
            </div>
        `;
    }
});

// معالجة الأخطاء العامة
window.addEventListener('error', function(event) {
    console.error('Global error:', event.error);
});

// معالجة الأخطاء غير المعالجة في Promise
window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled promise rejection:', event.reason);
});
