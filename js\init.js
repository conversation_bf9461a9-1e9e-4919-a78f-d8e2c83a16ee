/**
 * ملف التهيئة الرئيسي للتطبيق
 */

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing app...');
    
    // التحقق من وجود جميع الوحدات المطلوبة
    const requiredModules = ['DB', 'Auth', 'App'];
    const missingModules = [];

    requiredModules.forEach(module => {
        if (typeof window[module] === 'undefined') {
            missingModules.push(module);
        }
    });

    if (missingModules.length > 0) {
        console.error('Missing critical modules:', missingModules);
        document.body.innerHTML = `
            <div style="padding: 20px; text-align: center; font-family: 'Tajawal', Arial, sans-serif; direction: rtl;">
                <h2 style="color: #e74c3c;">خطأ في تحميل التطبيق</h2>
                <p>لم يتم تحميل الوحدات الأساسية التالية:</p>
                <ul style="list-style: none; color: #e74c3c; margin: 20px 0;">
                    ${missingModules.map(module => `<li style="margin: 5px 0;">• ${module}</li>`).join('')}
                </ul>
                <p>يرجى التحقق من ملفات JavaScript والمحاولة مرة أخرى.</p>
                <button onclick="location.reload()" style="padding: 10px 20px; font-size: 16px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer;">إعادة تحميل</button>
            </div>
        `;
        return;
    }

    // التحقق من وجود الوحدات الاختيارية
    const optionalModules = ['Dashboard', 'Products', 'Sales', 'Customers', 'Suppliers', 'Purchases', 'Reports', 'Settings'];
    const missingOptional = [];

    optionalModules.forEach(module => {
        if (typeof window[module] === 'undefined') {
            missingOptional.push(module);
        }
    });

    if (missingOptional.length > 0) {
        console.warn('Missing optional modules:', missingOptional);
    }
    
    try {
        // تهيئة قاعدة البيانات أولاً
        console.log('Initializing database...');
        DB.init();
        
        // تهيئة التطبيق
        console.log('Initializing app...');
        App.init();
        
        console.log('App initialized successfully');
    } catch (error) {
        console.error('Error initializing app:', error);
        document.body.innerHTML = `
            <div style="padding: 20px; text-align: center; font-family: Arial, sans-serif;">
                <h2>خطأ في تهيئة التطبيق</h2>
                <p>حدث خطأ أثناء تهيئة التطبيق:</p>
                <p style="color: red; font-family: monospace;">${error.message}</p>
                <button onclick="location.reload()" style="padding: 10px 20px; font-size: 16px;">إعادة تحميل</button>
            </div>
        `;
    }
});

// معالجة الأخطاء العامة
window.addEventListener('error', function(event) {
    console.error('Global error:', event.error);
});

// معالجة الأخطاء غير المعالجة في Promise
window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled promise rejection:', event.reason);
});
