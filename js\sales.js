/**
 * نظام المبيعات
 */
const Sales = {
    currentSale: {
        items: [],
        customerId: 1, // العميل الافتراضي "ضيف"
        total: 0,
        tax: 0,
        grandTotal: 0,
        paymentMethod: 'cash'
    },

    // عرض صفحة المبيعات
    show() {
        const content = document.getElementById('page-content');
        content.innerHTML = `
            <div class="page-header">
                <h2><i class="fas fa-shopping-cart"></i> نظام المبيعات</h2>
                <button id="new-sale-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> فاتورة مبيعات جديدة
                </button>
            </div>

            <div class="sales-tabs">
                <button class="tab-btn active" data-tab="list">قائمة المبيعات</button>
                <button class="tab-btn" data-tab="new">فاتورة جديدة</button>
            </div>

            <!-- قائمة المبيعات -->
            <div id="sales-list-tab" class="tab-content active">
                <div class="card">
                    <div class="search-bar">
                        <input type="text" id="sale-search" class="form-control" placeholder="البحث في المبيعات...">
                        <select id="customer-filter" class="form-control">
                            <option value="">جميع العملاء</option>
                        </select>
                        <select id="payment-filter" class="form-control">
                            <option value="">جميع طرق الدفع</option>
                            <option value="cash">نقد</option>
                            <option value="credit">آجل</option>
                        </select>
                    </div>

                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>التاريخ</th>
                                    <th>المجموع</th>
                                    <th>طريقة الدفع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="sales-table-body">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- فاتورة جديدة -->
            <div id="new-sale-tab" class="tab-content">
                <div class="sales-form-container">
                    <div class="card">
                        <h3>فاتورة مبيعات جديدة</h3>

                        <div class="sales-header">
                            <div class="form-group">
                                <label for="sale-customer">العميل *</label>
                                <select id="sale-customer" class="form-control" required>
                                    <option value="1">ضيف (نقد)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="sale-date">تاريخ الفاتورة</label>
                                <input type="date" id="sale-date" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="payment-method">طريقة الدفع</label>
                                <select id="payment-method" class="form-control">
                                    <option value="cash">نقد</option>
                                    <option value="credit">آجل</option>
                                </select>
                            </div>
                        </div>

                        <div class="sales-items">
                            <h4>أصناف الفاتورة</h4>
                            <div class="add-item-form">
                                <select id="item-product" class="form-control">
                                    <option value="">اختر المنتج</option>
                                </select>
                                <input type="number" id="item-quantity" class="form-control" placeholder="الكمية" min="1" value="1">
                                <input type="number" id="item-price" class="form-control" placeholder="السعر" step="0.01" readonly>
                                <button id="add-item-btn" class="btn btn-secondary">إضافة</button>
                            </div>

                            <div class="items-table">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>السعر</th>
                                            <th>المجموع</th>
                                            <th>إجراء</th>
                                        </tr>
                                    </thead>
                                    <tbody id="sale-items-body">
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="sales-totals">
                            <div class="totals-grid">
                                <div class="total-item">
                                    <label>المجموع الفرعي:</label>
                                    <span id="subtotal">٠.٠٠ ريال</span>
                                </div>
                                <div class="total-item">
                                    <label>الضريبة (<span id="tax-rate">١٥</span>%):</label>
                                    <span id="tax-amount">٠.٠٠ ريال</span>
                                </div>
                                <div class="total-item grand-total">
                                    <label>المجموع الإجمالي:</label>
                                    <span id="grand-total">٠.٠٠ ريال</span>
                                </div>
                            </div>
                        </div>

                        <div class="sales-actions">
                            <button id="save-sale-btn" class="btn btn-primary">حفظ الفاتورة</button>
                            <button id="save-print-sale-btn" class="btn btn-success">حفظ وطباعة</button>
                            <button id="clear-sale-btn" class="btn btn-secondary">مسح</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج عرض تفاصيل الفاتورة -->
            <div id="sale-details-modal" class="modal hidden">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3>تفاصيل الفاتورة</h3>
                        <button id="close-details-modal" class="btn-close" onclick="closeModal('sale-details-modal')">&times;</button>
                    </div>
                    <div id="sale-details-content"></div>
                    <div class="modal-footer">
                        <button onclick="closeModal('sale-details-modal')" class="btn btn-secondary">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                </div>
            </div>
        `;

        this.attachEvents();
        this.loadSales();
        this.loadCustomers();
        this.loadProducts();
        this.setCurrentDate();
        this.calculateTotals();
    },

    // إضافة الأحداث
    attachEvents() {
        // تبديل التبويبات
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // زر فاتورة جديدة
        document.getElementById('new-sale-btn').addEventListener('click', () => {
            this.switchTab('new');
        });

        // البحث والفلترة
        document.getElementById('sale-search').addEventListener('input', (e) => {
            this.searchSales(e.target.value);
        });

        document.getElementById('customer-filter').addEventListener('change', (e) => {
            this.filterByCustomer(e.target.value);
        });

        document.getElementById('payment-filter').addEventListener('change', (e) => {
            this.filterByPayment(e.target.value);
        });

        // إضافة صنف للفاتورة
        document.getElementById('add-item-btn').addEventListener('click', () => {
            this.addItemToSale();
        });

        // حفظ الفاتورة
        document.getElementById('save-sale-btn').addEventListener('click', () => {
            this.saveSale();
        });

        document.getElementById('save-print-sale-btn').addEventListener('click', () => {
            this.saveSale(true);
        });

        // مسح الفاتورة
        document.getElementById('clear-sale-btn').addEventListener('click', () => {
            this.clearSale();
        });

        // تحديث السعر عند اختيار المنتج
        document.getElementById('item-product').addEventListener('change', (e) => {
            this.updateItemPrice(e.target.value);
        });

        // تحديث طريقة الدفع عند تغيير العميل
        document.getElementById('sale-customer').addEventListener('change', (e) => {
            this.updatePaymentMethod(e.target.value);
        });

        // لا حاجة لمعالجات إضافية - يتم التعامل معها في النظام العام
    },

    // تبديل التبويبات
    switchTab(tab) {
        // إزالة الفئة النشطة من جميع التبويبات
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

        // إضافة الفئة النشطة للتبويب المحدد
        document.querySelector(`[data-tab="${tab}"]`).classList.add('active');
        document.getElementById(`${tab === 'list' ? 'sales-list' : 'new-sale'}-tab`).classList.add('active');
    },

    // تحميل المبيعات
    loadSales() {
        const data = DB.getData();
        const sales = data.sales || [];
        this.renderSales(sales);
    },

    // عرض المبيعات في الجدول
    renderSales(sales) {
        const tbody = document.getElementById('sales-table-body');
        tbody.innerHTML = '';

        if (sales.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center">لا توجد فواتير مبيعات</td>
                </tr>
            `;
            return;
        }

        const data = DB.getData();
        sales.forEach(sale => {
            const customer = data.customers.find(c => c.id === sale.customerId);
            const row = document.createElement('tr');

            const paymentText = sale.paymentMethod === 'cash' ? 'نقد' : 'آجل';
            const paymentClass = sale.paymentMethod === 'cash' ? 'cash' : 'credit';

            row.innerHTML = `
                <td>#${sale.id}</td>
                <td>${customer ? customer.name : 'غير محدد'}</td>
                <td>${new Date(sale.date).toLocaleDateString('ar-SA')}</td>
                <td>${this.formatCurrency(sale.grandTotal)}</td>
                <td><span class="payment-method ${paymentClass}">${paymentText}</span></td>
                <td class="actions">
                    <button onclick="Sales.viewSale(${sale.id})" class="btn btn-sm btn-info">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button onclick="Sales.printSale(${sale.id})" class="btn btn-sm btn-secondary">
                        <i class="fas fa-print"></i>
                    </button>
                    <button onclick="Sales.deleteSale(${sale.id})" class="btn btn-sm btn-danger">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    },

    // تحميل العملاء
    loadCustomers() {
        const data = DB.getData();
        const customers = data.customers || [];

        const customerSelect = document.getElementById('sale-customer');
        const customerFilter = document.getElementById('customer-filter');

        customerSelect.innerHTML = '';
        customerFilter.innerHTML = '<option value="">جميع العملاء</option>';

        customers.forEach(customer => {
            const option1 = new Option(customer.name, customer.id);
            const option2 = new Option(customer.name, customer.id);
            customerSelect.appendChild(option1);
            customerFilter.appendChild(option2);
        });

        // تعيين العميل الافتراضي
        customerSelect.value = 1;
    },

    // تحميل المنتجات
    loadProducts() {
        const data = DB.getData();
        const products = data.products.filter(p => p.quantity > 0) || [];

        const productSelect = document.getElementById('item-product');
        productSelect.innerHTML = '<option value="">اختر المنتج</option>';

        products.forEach(product => {
            const option = new Option(`${product.name} (متوفر: ${product.quantity})`, product.id);
            productSelect.appendChild(option);
        });
    },

    // تعيين التاريخ الحالي
    setCurrentDate() {
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('sale-date').value = today;
    },

    // تحديث سعر الصنف
    updateItemPrice(productId) {
        if (!productId) {
            document.getElementById('item-price').value = '';
            return;
        }

        const data = DB.getData();
        const product = data.products.find(p => p.id == productId);
        if (product) {
            document.getElementById('item-price').value = product.price || 0;
        }
    },

    // تحديث طريقة الدفع
    updatePaymentMethod(customerId) {
        const paymentMethodSelect = document.getElementById('payment-method');

        if (customerId == 1) { // العميل الافتراضي "ضيف"
            paymentMethodSelect.value = 'cash';
            paymentMethodSelect.disabled = true;
        } else {
            paymentMethodSelect.disabled = false;
        }

        this.currentSale.customerId = parseInt(customerId);
        this.currentSale.paymentMethod = paymentMethodSelect.value;
    },

    // إضافة صنف للفاتورة
    addItemToSale() {
        const productId = document.getElementById('item-product').value;
        const quantity = parseFloat(document.getElementById('item-quantity').value);
        const price = parseFloat(document.getElementById('item-price').value);

        if (!productId || !quantity || !price) {
            alert('يرجى ملء جميع بيانات الصنف');
            return;
        }

        const data = DB.getData();
        const product = data.products.find(p => p.id == productId);

        if (!product) {
            alert('المنتج غير موجود');
            return;
        }

        if (quantity > product.quantity) {
            alert(`الكمية المطلوبة (${quantity}) أكبر من المتوفر (${product.quantity})`);
            return;
        }

        // التحقق من وجود المنتج في الفاتورة
        const existingItemIndex = this.currentSale.items.findIndex(item => item.productId === parseInt(productId));

        if (existingItemIndex !== -1) {
            // تحديث الكمية للمنتج الموجود
            const newQuantity = this.currentSale.items[existingItemIndex].quantity + quantity;
            if (newQuantity > product.quantity) {
                alert(`إجمالي الكمية (${newQuantity}) أكبر من المتوفر (${product.quantity})`);
                return;
            }
            this.currentSale.items[existingItemIndex].quantity = newQuantity;
            this.currentSale.items[existingItemIndex].total = newQuantity * price;
        } else {
            // إضافة منتج جديد
            const item = {
                productId: parseInt(productId),
                productName: product.name,
                quantity: quantity,
                price: price,
                total: quantity * price
            };
            this.currentSale.items.push(item);
        }

        this.renderSaleItems();
        this.calculateTotals();
        this.clearItemForm();
    },

    // عرض أصناف الفاتورة
    renderSaleItems() {
        const tbody = document.getElementById('sale-items-body');
        tbody.innerHTML = '';

        this.currentSale.items.forEach((item, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.productName}</td>
                <td>${item.quantity}</td>
                <td>${this.formatCurrency(item.price)}</td>
                <td>${this.formatCurrency(item.total)}</td>
                <td>
                    <button onclick="Sales.removeItem(${index})" class="btn btn-sm btn-danger">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    },

    // إزالة صنف من الفاتورة
    removeItem(index) {
        this.currentSale.items.splice(index, 1);
        this.renderSaleItems();
        this.calculateTotals();
    },

    // حساب المجاميع
    calculateTotals() {
        const subtotal = this.currentSale.items.reduce((sum, item) => sum + item.total, 0);
        const data = DB.getData();
        const taxRate = data.settings.taxRate || 15;
        const tax = subtotal * (taxRate / 100);
        const grandTotal = subtotal + tax;

        this.currentSale.total = subtotal;
        this.currentSale.tax = tax;
        this.currentSale.grandTotal = grandTotal;

        document.getElementById('subtotal').textContent = this.formatCurrency(subtotal);
        document.getElementById('tax-rate').textContent = taxRate;
        document.getElementById('tax-amount').textContent = this.formatCurrency(tax);
        document.getElementById('grand-total').textContent = this.formatCurrency(grandTotal);
    },

    // مسح نموذج الصنف
    clearItemForm() {
        document.getElementById('item-product').value = '';
        document.getElementById('item-quantity').value = '1';
        document.getElementById('item-price').value = '';
    },

    // حفظ الفاتورة
    saveSale(shouldPrint = false) {
        const customerId = document.getElementById('sale-customer').value;
        const date = document.getElementById('sale-date').value;
        const paymentMethod = document.getElementById('payment-method').value;

        if (!customerId) {
            alert('يرجى اختيار العميل');
            return;
        }

        if (this.currentSale.items.length === 0) {
            alert('يرجى إضافة أصناف للفاتورة');
            return;
        }

        const data = DB.getData();
        const sale = {
            id: DB.getNextId('sale'),
            customerId: parseInt(customerId),
            date: date,
            items: [...this.currentSale.items],
            total: this.currentSale.total,
            tax: this.currentSale.tax,
            grandTotal: this.currentSale.grandTotal,
            paymentMethod: paymentMethod,
            createdAt: new Date().toISOString()
        };

        // إضافة الفاتورة
        data.sales.push(sale);

        // تحديث المخزون
        sale.items.forEach(item => {
            const productIndex = data.products.findIndex(p => p.id === item.productId);
            if (productIndex !== -1) {
                data.products[productIndex].quantity -= item.quantity;
            }
        });

        // تحديث رصيد العميل (للمبيعات الآجلة)
        if (paymentMethod === 'credit') {
            const customerIndex = data.customers.findIndex(c => c.id === parseInt(customerId));
            if (customerIndex !== -1) {
                data.customers[customerIndex].balance += sale.grandTotal;
            }
        }

        DB.saveData(data);

        if (shouldPrint) {
            this.printSale(sale.id);
        }

        this.clearSale();
        this.loadSales();
        this.loadProducts(); // تحديث قائمة المنتجات
        this.switchTab('list');

        alert('تم حفظ فاتورة المبيعات بنجاح');
    },

    // مسح الفاتورة
    clearSale() {
        this.currentSale = {
            items: [],
            customerId: 1,
            total: 0,
            tax: 0,
            grandTotal: 0,
            paymentMethod: 'cash'
        };

        document.getElementById('sale-customer').value = '1';
        document.getElementById('payment-method').value = 'cash';
        document.getElementById('payment-method').disabled = true;
        this.setCurrentDate();
        this.clearItemForm();
        this.renderSaleItems();
        this.calculateTotals();
    },

    // البحث في المبيعات
    searchSales(query) {
        const sales = DB.search('sales', query, []);
        this.renderSales(sales);
    },

    // فلترة حسب العميل
    filterByCustomer(customerId) {
        const data = DB.getData();
        let sales = data.sales || [];

        if (customerId) {
            sales = sales.filter(s => s.customerId == customerId);
        }

        this.renderSales(sales);
    },

    // فلترة حسب طريقة الدفع
    filterByPayment(paymentMethod) {
        const data = DB.getData();
        let sales = data.sales || [];

        if (paymentMethod) {
            sales = sales.filter(s => s.paymentMethod === paymentMethod);
        }

        this.renderSales(sales);
    },

    // عرض تفاصيل الفاتورة
    viewSale(id) {
        const data = DB.getData();
        const sale = data.sales.find(s => s.id === id);
        if (!sale) return;

        const customer = data.customers.find(c => c.id === sale.customerId);
        const paymentText = sale.paymentMethod === 'cash' ? 'نقد' : 'آجل';

        const content = document.getElementById('sale-details-content');
        content.innerHTML = `
            <div style="padding: 20px;">
                <div class="sale-header-info">
                    <h4>معلومات الفاتورة</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>رقم الفاتورة:</label>
                            <span>#${sale.id}</span>
                        </div>
                        <div class="info-item">
                            <label>العميل:</label>
                            <span>${customer ? customer.name : 'غير محدد'}</span>
                        </div>
                        <div class="info-item">
                            <label>التاريخ:</label>
                            <span>${new Date(sale.date).toLocaleDateString('ar-SA')}</span>
                        </div>
                        <div class="info-item">
                            <label>طريقة الدفع:</label>
                            <span>${paymentText}</span>
                        </div>
                    </div>
                </div>

                <div class="sale-items-info">
                    <h4>أصناف الفاتورة</h4>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${sale.items.map(item => `
                                <tr>
                                    <td>${item.productName}</td>
                                    <td>${item.quantity}</td>
                                    <td>${this.formatCurrency(item.price)}</td>
                                    <td>${this.formatCurrency(item.total)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>

                <div class="sale-totals-info">
                    <div class="totals-grid">
                        <div class="total-item">
                            <label>المجموع الفرعي:</label>
                            <span>${this.formatCurrency(sale.total)}</span>
                        </div>
                        <div class="total-item">
                            <label>الضريبة:</label>
                            <span>${this.formatCurrency(sale.tax)}</span>
                        </div>
                        <div class="total-item grand-total">
                            <label>المجموع الإجمالي:</label>
                            <span>${this.formatCurrency(sale.grandTotal)}</span>
                        </div>
                    </div>
                </div>

                <div class="sale-actions" style="margin-top: 20px;">
                    <button onclick="Sales.printSale(${sale.id})" class="btn btn-primary">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </div>
        `;

        document.getElementById('sale-details-modal').classList.remove('hidden');
    },

    // إغلاق نافذة تفاصيل الفاتورة
    closeSaleDetails() {
        const modal = document.getElementById('sale-details-modal');
        if (modal) {
            modal.classList.add('hidden');
            console.log('تم إغلاق نافذة تفاصيل الفاتورة');
        }
    },

    // إخفاء نموذج التفاصيل
    hideDetailsModal() {
        document.getElementById('sale-details-modal').classList.add('hidden');
    },

    // طباعة الفاتورة
    printSale(id) {
        // سيتم تطوير هذه الوظيفة لاحقاً
        alert('طباعة الفاتورة #' + id);
    },

    // حذف الفاتورة
    deleteSale(id) {
        if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
            const data = DB.getData();
            const sale = data.sales.find(s => s.id === id);

            if (sale) {
                // إرجاع المخزون
                sale.items.forEach(item => {
                    const productIndex = data.products.findIndex(p => p.id === item.productId);
                    if (productIndex !== -1) {
                        data.products[productIndex].quantity += item.quantity;
                    }
                });

                // تحديث رصيد العميل (للمبيعات الآجلة)
                if (sale.paymentMethod === 'credit') {
                    const customerIndex = data.customers.findIndex(c => c.id === sale.customerId);
                    if (customerIndex !== -1) {
                        data.customers[customerIndex].balance -= sale.grandTotal;
                    }
                }
            }

            data.sales = data.sales.filter(s => s.id !== id);
            DB.saveData(data);
            this.loadSales();
            this.loadProducts();
            alert('تم حذف الفاتورة بنجاح');
        }
    },

    // إنشاء فاتورة جديدة لعميل محدد
    newSaleForCustomer(customerId) {
        this.switchTab('new');
        document.getElementById('sale-customer').value = customerId;
        this.updatePaymentMethod(customerId);
    },

    // تنسيق العملة
    formatCurrency(amount) {
        const data = DB.getData();
        const currency = data.settings.currency || 'ريال';
        return `${amount.toFixed(2)} ${currency}`;
    }
};


