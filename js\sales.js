/**
 * نظام المبيعات
 */
const Sales = {
    currentSale: {
        items: [],
        customerId: 1, // العميل الافتراضي "ضيف"
        total: 0,
        tax: 0,
        grandTotal: 0,
        paymentMethod: 'cash'
    },

    // عرض صفحة المبيعات
    show() {
        const content = document.getElementById('page-content');
        content.innerHTML = `
            <div class="page-header">
                <h2><i class="fas fa-shopping-cart"></i> نظام المبيعات</h2>
                <div>
                    <button id="new-sale-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> فاتورة سريعة
                    </button>
                    <button id="new-detailed-sale-btn" class="btn btn-success">
                        <i class="fas fa-file-invoice-dollar"></i> فاتورة بكامل البيانات
                    </button>
                </div>
            </div>

            <div class="sales-tabs">
                <button class="tab-btn active" data-tab="list">قائمة المبيعات</button>
                <button class="tab-btn" data-tab="new">فاتورة جديدة</button>
            </div>

            <!-- قائمة المبيعات -->
            <div id="sales-list-tab" class="tab-content active">
                <div class="card">
                    <div class="search-bar">
                        <input type="text" id="sale-search" class="form-control" placeholder="البحث في المبيعات...">
                        <select id="customer-filter" class="form-control">
                            <option value="">جميع العملاء</option>
                        </select>
                        <select id="payment-filter" class="form-control">
                            <option value="">جميع طرق الدفع</option>
                            <option value="cash">نقد</option>
                            <option value="credit">آجل</option>
                        </select>
                    </div>

                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>التاريخ</th>
                                    <th>المجموع</th>
                                    <th>طريقة الدفع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="sales-table-body">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- فاتورة جديدة -->
            <div id="new-sale-tab" class="tab-content">
                <div class="sales-form-container">
                    <div class="card">
                        <h3>فاتورة مبيعات جديدة</h3>

                        <div class="sales-header">
                            <div class="form-group">
                                <label for="sale-customer">العميل *</label>
                                <select id="sale-customer" class="form-control" required>
                                    <option value="1">ضيف (نقد)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="sale-date">تاريخ الفاتورة</label>
                                <input type="date" id="sale-date" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="payment-method">طريقة الدفع</label>
                                <select id="payment-method" class="form-control">
                                    <option value="cash">نقد</option>
                                    <option value="credit">آجل</option>
                                </select>
                            </div>
                        </div>

                        <div class="sales-items">
                            <h4>أصناف الفاتورة</h4>
                            <div class="add-item-form">
                                <select id="item-product" class="form-control">
                                    <option value="">اختر المنتج</option>
                                </select>
                                <input type="number" id="item-quantity" class="form-control" placeholder="الكمية" min="1" value="1">
                                <input type="number" id="item-price" class="form-control" placeholder="السعر" step="0.01" readonly>
                                <button id="add-item-btn" class="btn btn-secondary">إضافة</button>
                            </div>

                            <div class="items-table">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>السعر</th>
                                            <th>المجموع</th>
                                            <th>إجراء</th>
                                        </tr>
                                    </thead>
                                    <tbody id="sale-items-body">
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="sales-totals">
                            <div class="totals-grid">
                                <div class="total-item">
                                    <label>المجموع الفرعي:</label>
                                    <span id="subtotal">٠.٠٠ ريال</span>
                                </div>
                                <div class="total-item">
                                    <label>الضريبة (<span id="tax-rate">١٥</span>%):</label>
                                    <span id="tax-amount">٠.٠٠ ريال</span>
                                </div>
                                <div class="total-item grand-total">
                                    <label>المجموع الإجمالي:</label>
                                    <span id="grand-total">٠.٠٠ ريال</span>
                                </div>
                            </div>
                        </div>

                        <div class="sales-actions">
                            <button id="save-sale-btn" class="btn btn-primary">حفظ الفاتورة</button>
                            <button id="save-print-sale-btn" class="btn btn-success">حفظ وطباعة</button>
                            <button id="clear-sale-btn" class="btn btn-secondary">مسح</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج عرض تفاصيل الفاتورة -->
            <div id="sale-details-modal" class="modal hidden">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3>تفاصيل الفاتورة</h3>
                        <button id="close-details-modal" class="btn-close" onclick="closeModal('sale-details-modal')">&times;</button>
                    </div>
                    <div id="sale-details-content"></div>
                    <div class="modal-footer">
                        <button onclick="closeModal('sale-details-modal')" class="btn btn-secondary">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                </div>
            </div>

            <!-- نموذج فاتورة مبيعات بكامل البيانات -->
            <div id="detailed-sale-modal" class="modal hidden">
                <div class="modal-content extra-large">
                    <div class="modal-header">
                        <h3>فاتورة مبيعات بكامل البيانات</h3>
                        <button id="close-detailed-sale-modal" class="btn-close" onclick="Sales.closeDetailedSaleModal()">&times;</button>
                    </div>
                    <form id="detailed-sale-form" class="detailed-sale-form">
                        <!-- معلومات الفاتورة الأساسية -->
                        <div class="form-section">
                            <h4><i class="fas fa-file-invoice"></i> معلومات الفاتورة</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-sale-number">رقم الفاتورة</label>
                                    <input type="text" id="detailed-sale-number" class="form-control" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-sale-date">تاريخ الفاتورة *</label>
                                    <input type="date" id="detailed-sale-date" class="form-control" required>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-sale-time">وقت الفاتورة</label>
                                    <input type="time" id="detailed-sale-time" class="form-control">
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-sale-reference">رقم المرجع</label>
                                    <input type="text" id="detailed-sale-reference" class="form-control" placeholder="رقم مرجعي اختياري">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-sale-type">نوع الفاتورة</label>
                                    <select id="detailed-sale-type" class="form-control">
                                        <option value="sale">مبيعات عادية</option>
                                        <option value="return">مرتجع مبيعات</option>
                                        <option value="exchange">استبدال</option>
                                        <option value="sample">عينة</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-sale-status">حالة الفاتورة</label>
                                    <select id="detailed-sale-status" class="form-control">
                                        <option value="draft">مسودة</option>
                                        <option value="confirmed">مؤكدة</option>
                                        <option value="delivered">مسلمة</option>
                                        <option value="cancelled">ملغية</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات العميل -->
                        <div class="form-section">
                            <h4><i class="fas fa-user"></i> معلومات العميل</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-sale-customer">العميل *</label>
                                    <select id="detailed-sale-customer" class="form-control" required>
                                        <option value="">اختر العميل</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-phone">هاتف العميل</label>
                                    <input type="tel" id="detailed-customer-phone" class="form-control" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-email">بريد العميل</label>
                                    <input type="email" id="detailed-customer-email" class="form-control" readonly>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="detailed-customer-address">عنوان العميل</label>
                                <textarea id="detailed-customer-address" class="form-control" rows="2" readonly></textarea>
                            </div>
                        </div>

                        <!-- تفاصيل الدفع والشحن -->
                        <div class="form-section">
                            <h4><i class="fas fa-credit-card"></i> تفاصيل الدفع والشحن</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-payment-method">طريقة الدفع *</label>
                                    <select id="detailed-payment-method" class="form-control" required>
                                        <option value="cash">نقد</option>
                                        <option value="credit">آجل</option>
                                        <option value="card">بطاقة ائتمان</option>
                                        <option value="bank_transfer">تحويل بنكي</option>
                                        <option value="check">شيك</option>
                                        <option value="installment">تقسيط</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-payment-terms">شروط الدفع (أيام)</label>
                                    <input type="number" id="detailed-payment-terms" class="form-control" min="0" value="0">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-due-date">تاريخ الاستحقاق</label>
                                    <input type="date" id="detailed-due-date" class="form-control">
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-shipping-method">طريقة الشحن</label>
                                    <select id="detailed-shipping-method" class="form-control">
                                        <option value="pickup">استلام من المحل</option>
                                        <option value="delivery">توصيل</option>
                                        <option value="shipping">شحن</option>
                                        <option value="courier">شركة شحن</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-shipping-cost">تكلفة الشحن</label>
                                    <input type="number" id="detailed-shipping-cost" class="form-control" step="0.01" min="0" value="0">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-delivery-date">تاريخ التسليم المتوقع</label>
                                    <input type="date" id="detailed-delivery-date" class="form-control">
                                </div>
                            </div>
                        </div>

                        <!-- أصناف الفاتورة -->
                        <div class="form-section">
                            <h4><i class="fas fa-list"></i> أصناف الفاتورة</h4>

                            <!-- نموذج إضافة صنف -->
                            <div class="add-item-section">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="detailed-item-product">المنتج *</label>
                                        <select id="detailed-item-product" class="form-control">
                                            <option value="">اختر المنتج</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="detailed-item-quantity">الكمية *</label>
                                        <input type="number" id="detailed-item-quantity" class="form-control" min="1" value="1" step="0.01">
                                    </div>
                                    <div class="form-group">
                                        <label for="detailed-item-price">السعر *</label>
                                        <input type="number" id="detailed-item-price" class="form-control" step="0.01" min="0">
                                    </div>
                                </div>
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="detailed-item-discount">خصم الصنف (%)</label>
                                        <input type="number" id="detailed-item-discount" class="form-control" step="0.01" min="0" max="100" value="0">
                                    </div>
                                    <div class="form-group">
                                        <label for="detailed-item-tax">ضريبة الصنف (%)</label>
                                        <input type="number" id="detailed-item-tax" class="form-control" step="0.01" min="0" value="15">
                                    </div>
                                    <div class="form-group">
                                        <label for="detailed-item-notes">ملاحظات الصنف</label>
                                        <input type="text" id="detailed-item-notes" class="form-control">
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button type="button" onclick="Sales.addDetailedItem()" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> إضافة الصنف
                                    </button>
                                    <button type="button" onclick="Sales.clearDetailedItemForm()" class="btn btn-secondary">
                                        <i class="fas fa-eraser"></i> مسح
                                    </button>
                                </div>
                            </div>

                            <!-- جدول الأصناف -->
                            <div class="items-table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>السعر</th>
                                            <th>الخصم</th>
                                            <th>الضريبة</th>
                                            <th>المجموع</th>
                                            <th>ملاحظات</th>
                                            <th>إجراء</th>
                                        </tr>
                                    </thead>
                                    <tbody id="detailed-sale-items-body">
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- المجاميع والخصومات -->
                        <div class="form-section">
                            <h4><i class="fas fa-calculator"></i> المجاميع والخصومات</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-discount-type">نوع الخصم العام</label>
                                    <select id="detailed-discount-type" class="form-control">
                                        <option value="percentage">نسبة مئوية</option>
                                        <option value="fixed">مبلغ ثابت</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-discount-value">قيمة الخصم العام</label>
                                    <input type="number" id="detailed-discount-value" class="form-control" step="0.01" min="0" value="0">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-tax-rate">معدل الضريبة العام (%)</label>
                                    <input type="number" id="detailed-tax-rate" class="form-control" step="0.01" min="0" value="15">
                                </div>
                            </div>

                            <!-- عرض المجاميع -->
                            <div class="totals-display">
                                <div class="total-row">
                                    <label>المجموع الفرعي:</label>
                                    <span id="detailed-subtotal">0.00 ريال</span>
                                </div>
                                <div class="total-row">
                                    <label>إجمالي الخصم:</label>
                                    <span id="detailed-total-discount">0.00 ريال</span>
                                </div>
                                <div class="total-row">
                                    <label>المجموع بعد الخصم:</label>
                                    <span id="detailed-after-discount">0.00 ريال</span>
                                </div>
                                <div class="total-row">
                                    <label>إجمالي الضريبة:</label>
                                    <span id="detailed-total-tax">0.00 ريال</span>
                                </div>
                                <div class="total-row">
                                    <label>تكلفة الشحن:</label>
                                    <span id="detailed-shipping-total">0.00 ريال</span>
                                </div>
                                <div class="total-row grand-total">
                                    <label>المجموع الإجمالي:</label>
                                    <span id="detailed-grand-total">0.00 ريال</span>
                                </div>
                            </div>
                        </div>

                        <!-- ملاحظات إضافية -->
                        <div class="form-section">
                            <h4><i class="fas fa-sticky-note"></i> ملاحظات إضافية</h4>
                            <div class="form-group">
                                <label for="detailed-sale-notes">ملاحظات الفاتورة</label>
                                <textarea id="detailed-sale-notes" class="form-control" rows="3" placeholder="أي ملاحظات أو تعليمات خاصة بالفاتورة..."></textarea>
                            </div>
                            <div class="form-group">
                                <label for="detailed-internal-notes">ملاحظات داخلية</label>
                                <textarea id="detailed-internal-notes" class="form-control" rows="2" placeholder="ملاحظات للاستخدام الداخلي فقط..."></textarea>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="form-actions">
                            <button type="button" onclick="Sales.saveDetailedSale()" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الفاتورة
                            </button>
                            <button type="button" onclick="Sales.saveAndPrintDetailedSale()" class="btn btn-success">
                                <i class="fas fa-print"></i> حفظ وطباعة
                            </button>
                            <button type="button" onclick="Sales.closeDetailedSaleModal()" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                            <button type="button" onclick="Sales.clearDetailedSaleForm()" class="btn btn-warning">
                                <i class="fas fa-eraser"></i> مسح النموذج
                            </button>
                            <button type="button" onclick="App.goBack()" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        this.attachEvents();
        this.loadSales();
        this.loadCustomers();
        this.loadProducts();
        this.setCurrentDate();
        this.calculateTotals();
    },

    // إضافة الأحداث
    attachEvents() {
        // تبديل التبويبات
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // زر فاتورة جديدة (سريعة)
        document.getElementById('new-sale-btn').addEventListener('click', () => {
            this.switchTab('new');
        });

        // زر فاتورة بكامل البيانات
        document.getElementById('new-detailed-sale-btn').addEventListener('click', () => {
            this.showDetailedSaleModal();
        });

        // البحث والفلترة
        document.getElementById('sale-search').addEventListener('input', (e) => {
            this.searchSales(e.target.value);
        });

        document.getElementById('customer-filter').addEventListener('change', (e) => {
            this.filterByCustomer(e.target.value);
        });

        document.getElementById('payment-filter').addEventListener('change', (e) => {
            this.filterByPayment(e.target.value);
        });

        // إضافة صنف للفاتورة
        document.getElementById('add-item-btn').addEventListener('click', () => {
            this.addItemToSale();
        });

        // حفظ الفاتورة
        document.getElementById('save-sale-btn').addEventListener('click', () => {
            this.saveSale();
        });

        document.getElementById('save-print-sale-btn').addEventListener('click', () => {
            this.saveSale(true);
        });

        // مسح الفاتورة
        document.getElementById('clear-sale-btn').addEventListener('click', () => {
            this.clearSale();
        });

        // تحديث السعر عند اختيار المنتج
        document.getElementById('item-product').addEventListener('change', (e) => {
            this.updateItemPrice(e.target.value);
        });

        // تحديث طريقة الدفع عند تغيير العميل
        document.getElementById('sale-customer').addEventListener('change', (e) => {
            this.updatePaymentMethod(e.target.value);
        });

        // لا حاجة لمعالجات إضافية - يتم التعامل معها في النظام العام
    },

    // تبديل التبويبات
    switchTab(tab) {
        // إزالة الفئة النشطة من جميع التبويبات
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

        // إضافة الفئة النشطة للتبويب المحدد
        document.querySelector(`[data-tab="${tab}"]`).classList.add('active');
        document.getElementById(`${tab === 'list' ? 'sales-list' : 'new-sale'}-tab`).classList.add('active');
    },

    // تحميل المبيعات
    loadSales() {
        const data = DB.getData();
        const sales = data.sales || [];
        this.renderSales(sales);
    },

    // عرض المبيعات في الجدول
    renderSales(sales) {
        const tbody = document.getElementById('sales-table-body');
        tbody.innerHTML = '';

        if (sales.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center">لا توجد فواتير مبيعات</td>
                </tr>
            `;
            return;
        }

        const data = DB.getData();
        sales.forEach(sale => {
            const customer = data.customers.find(c => c.id === sale.customerId);
            const row = document.createElement('tr');

            const paymentText = sale.paymentMethod === 'cash' ? 'نقد' : 'آجل';
            const paymentClass = sale.paymentMethod === 'cash' ? 'cash' : 'credit';

            row.innerHTML = `
                <td>#${sale.id}</td>
                <td>${customer ? customer.name : 'غير محدد'}</td>
                <td>${new Date(sale.date).toLocaleDateString('ar-SA')}</td>
                <td>${this.formatCurrency(sale.grandTotal)}</td>
                <td><span class="payment-method ${paymentClass}">${paymentText}</span></td>
                <td class="actions">
                    <button onclick="Sales.viewSale(${sale.id})" class="btn btn-sm btn-info">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button onclick="Sales.printSale(${sale.id})" class="btn btn-sm btn-secondary">
                        <i class="fas fa-print"></i>
                    </button>
                    <button onclick="Sales.deleteSale(${sale.id})" class="btn btn-sm btn-danger">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    },

    // تحميل العملاء
    loadCustomers() {
        const data = DB.getData();
        const customers = data.customers || [];

        const customerSelect = document.getElementById('sale-customer');
        const customerFilter = document.getElementById('customer-filter');

        customerSelect.innerHTML = '';
        customerFilter.innerHTML = '<option value="">جميع العملاء</option>';

        customers.forEach(customer => {
            const option1 = new Option(customer.name, customer.id);
            const option2 = new Option(customer.name, customer.id);
            customerSelect.appendChild(option1);
            customerFilter.appendChild(option2);
        });

        // تعيين العميل الافتراضي
        customerSelect.value = 1;
    },

    // تحميل المنتجات
    loadProducts() {
        const data = DB.getData();
        const products = data.products.filter(p => p.quantity > 0) || [];

        const productSelect = document.getElementById('item-product');
        productSelect.innerHTML = '<option value="">اختر المنتج</option>';

        products.forEach(product => {
            const option = new Option(`${product.name} (متوفر: ${product.quantity})`, product.id);
            productSelect.appendChild(option);
        });
    },

    // تعيين التاريخ الحالي
    setCurrentDate() {
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('sale-date').value = today;
    },

    // تحديث سعر الصنف
    updateItemPrice(productId) {
        if (!productId) {
            document.getElementById('item-price').value = '';
            return;
        }

        const data = DB.getData();
        const product = data.products.find(p => p.id == productId);
        if (product) {
            document.getElementById('item-price').value = product.price || 0;
        }
    },

    // تحديث طريقة الدفع
    updatePaymentMethod(customerId) {
        const paymentMethodSelect = document.getElementById('payment-method');

        if (customerId == 1) { // العميل الافتراضي "ضيف"
            paymentMethodSelect.value = 'cash';
            paymentMethodSelect.disabled = true;
        } else {
            paymentMethodSelect.disabled = false;
        }

        this.currentSale.customerId = parseInt(customerId);
        this.currentSale.paymentMethod = paymentMethodSelect.value;
    },

    // إضافة صنف للفاتورة
    addItemToSale() {
        const productId = document.getElementById('item-product').value;
        const quantity = parseFloat(document.getElementById('item-quantity').value);
        const price = parseFloat(document.getElementById('item-price').value);

        if (!productId || !quantity || !price) {
            alert('يرجى ملء جميع بيانات الصنف');
            return;
        }

        const data = DB.getData();
        const product = data.products.find(p => p.id == productId);

        if (!product) {
            alert('المنتج غير موجود');
            return;
        }

        if (quantity > product.quantity) {
            alert(`الكمية المطلوبة (${quantity}) أكبر من المتوفر (${product.quantity})`);
            return;
        }

        // التحقق من وجود المنتج في الفاتورة
        const existingItemIndex = this.currentSale.items.findIndex(item => item.productId === parseInt(productId));

        if (existingItemIndex !== -1) {
            // تحديث الكمية للمنتج الموجود
            const newQuantity = this.currentSale.items[existingItemIndex].quantity + quantity;
            if (newQuantity > product.quantity) {
                alert(`إجمالي الكمية (${newQuantity}) أكبر من المتوفر (${product.quantity})`);
                return;
            }
            this.currentSale.items[existingItemIndex].quantity = newQuantity;
            this.currentSale.items[existingItemIndex].total = newQuantity * price;
        } else {
            // إضافة منتج جديد
            const item = {
                productId: parseInt(productId),
                productName: product.name,
                quantity: quantity,
                price: price,
                total: quantity * price
            };
            this.currentSale.items.push(item);
        }

        this.renderSaleItems();
        this.calculateTotals();
        this.clearItemForm();
    },

    // عرض أصناف الفاتورة
    renderSaleItems() {
        const tbody = document.getElementById('sale-items-body');
        tbody.innerHTML = '';

        this.currentSale.items.forEach((item, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.productName}</td>
                <td>${item.quantity}</td>
                <td>${this.formatCurrency(item.price)}</td>
                <td>${this.formatCurrency(item.total)}</td>
                <td>
                    <button onclick="Sales.removeItem(${index})" class="btn btn-sm btn-danger">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    },

    // إزالة صنف من الفاتورة
    removeItem(index) {
        this.currentSale.items.splice(index, 1);
        this.renderSaleItems();
        this.calculateTotals();
    },

    // حساب المجاميع
    calculateTotals() {
        const subtotal = this.currentSale.items.reduce((sum, item) => sum + item.total, 0);
        const data = DB.getData();
        const taxRate = data.settings.taxRate || 15;
        const tax = subtotal * (taxRate / 100);
        const grandTotal = subtotal + tax;

        this.currentSale.total = subtotal;
        this.currentSale.tax = tax;
        this.currentSale.grandTotal = grandTotal;

        document.getElementById('subtotal').textContent = this.formatCurrency(subtotal);
        document.getElementById('tax-rate').textContent = taxRate;
        document.getElementById('tax-amount').textContent = this.formatCurrency(tax);
        document.getElementById('grand-total').textContent = this.formatCurrency(grandTotal);
    },

    // مسح نموذج الصنف
    clearItemForm() {
        document.getElementById('item-product').value = '';
        document.getElementById('item-quantity').value = '1';
        document.getElementById('item-price').value = '';
    },

    // حفظ الفاتورة
    saveSale(shouldPrint = false) {
        const customerId = document.getElementById('sale-customer').value;
        const date = document.getElementById('sale-date').value;
        const paymentMethod = document.getElementById('payment-method').value;

        if (!customerId) {
            alert('يرجى اختيار العميل');
            return;
        }

        if (this.currentSale.items.length === 0) {
            alert('يرجى إضافة أصناف للفاتورة');
            return;
        }

        const data = DB.getData();
        const sale = {
            id: DB.getNextId('sale'),
            customerId: parseInt(customerId),
            date: date,
            items: [...this.currentSale.items],
            total: this.currentSale.total,
            tax: this.currentSale.tax,
            grandTotal: this.currentSale.grandTotal,
            paymentMethod: paymentMethod,
            createdAt: new Date().toISOString()
        };

        // إضافة الفاتورة
        data.sales.push(sale);

        // تحديث المخزون
        sale.items.forEach(item => {
            const productIndex = data.products.findIndex(p => p.id === item.productId);
            if (productIndex !== -1) {
                data.products[productIndex].quantity -= item.quantity;
            }
        });

        // تحديث رصيد العميل (للمبيعات الآجلة)
        if (paymentMethod === 'credit') {
            const customerIndex = data.customers.findIndex(c => c.id === parseInt(customerId));
            if (customerIndex !== -1) {
                data.customers[customerIndex].balance += sale.grandTotal;
            }
        }

        DB.saveData(data);

        if (shouldPrint) {
            this.printSale(sale.id);
        }

        this.clearSale();
        this.loadSales();
        this.loadProducts(); // تحديث قائمة المنتجات
        this.switchTab('list');

        alert('تم حفظ فاتورة المبيعات بنجاح');
    },

    // مسح الفاتورة
    clearSale() {
        this.currentSale = {
            items: [],
            customerId: 1,
            total: 0,
            tax: 0,
            grandTotal: 0,
            paymentMethod: 'cash'
        };

        document.getElementById('sale-customer').value = '1';
        document.getElementById('payment-method').value = 'cash';
        document.getElementById('payment-method').disabled = true;
        this.setCurrentDate();
        this.clearItemForm();
        this.renderSaleItems();
        this.calculateTotals();
    },

    // البحث في المبيعات
    searchSales(query) {
        const sales = DB.search('sales', query, []);
        this.renderSales(sales);
    },

    // فلترة حسب العميل
    filterByCustomer(customerId) {
        const data = DB.getData();
        let sales = data.sales || [];

        if (customerId) {
            sales = sales.filter(s => s.customerId == customerId);
        }

        this.renderSales(sales);
    },

    // فلترة حسب طريقة الدفع
    filterByPayment(paymentMethod) {
        const data = DB.getData();
        let sales = data.sales || [];

        if (paymentMethod) {
            sales = sales.filter(s => s.paymentMethod === paymentMethod);
        }

        this.renderSales(sales);
    },

    // عرض تفاصيل الفاتورة
    viewSale(id) {
        const data = DB.getData();
        const sale = data.sales.find(s => s.id === id);
        if (!sale) return;

        const customer = data.customers.find(c => c.id === sale.customerId);
        const paymentText = sale.paymentMethod === 'cash' ? 'نقد' : 'آجل';

        const content = document.getElementById('sale-details-content');
        content.innerHTML = `
            <div style="padding: 20px;">
                <div class="sale-header-info">
                    <h4>معلومات الفاتورة</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>رقم الفاتورة:</label>
                            <span>#${sale.id}</span>
                        </div>
                        <div class="info-item">
                            <label>العميل:</label>
                            <span>${customer ? customer.name : 'غير محدد'}</span>
                        </div>
                        <div class="info-item">
                            <label>التاريخ:</label>
                            <span>${new Date(sale.date).toLocaleDateString('ar-SA')}</span>
                        </div>
                        <div class="info-item">
                            <label>طريقة الدفع:</label>
                            <span>${paymentText}</span>
                        </div>
                    </div>
                </div>

                <div class="sale-items-info">
                    <h4>أصناف الفاتورة</h4>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${sale.items.map(item => `
                                <tr>
                                    <td>${item.productName}</td>
                                    <td>${item.quantity}</td>
                                    <td>${this.formatCurrency(item.price)}</td>
                                    <td>${this.formatCurrency(item.total)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>

                <div class="sale-totals-info">
                    <div class="totals-grid">
                        <div class="total-item">
                            <label>المجموع الفرعي:</label>
                            <span>${this.formatCurrency(sale.total)}</span>
                        </div>
                        <div class="total-item">
                            <label>الضريبة:</label>
                            <span>${this.formatCurrency(sale.tax)}</span>
                        </div>
                        <div class="total-item grand-total">
                            <label>المجموع الإجمالي:</label>
                            <span>${this.formatCurrency(sale.grandTotal)}</span>
                        </div>
                    </div>
                </div>

                <div class="sale-actions" style="margin-top: 20px;">
                    <button onclick="Sales.printSale(${sale.id})" class="btn btn-primary">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </div>
        `;

        document.getElementById('sale-details-modal').classList.remove('hidden');
    },

    // إغلاق نافذة تفاصيل الفاتورة
    closeSaleDetails() {
        const modal = document.getElementById('sale-details-modal');
        if (modal) {
            modal.classList.add('hidden');
            console.log('تم إغلاق نافذة تفاصيل الفاتورة');
        }
    },

    // إخفاء نموذج التفاصيل
    hideDetailsModal() {
        document.getElementById('sale-details-modal').classList.add('hidden');
    },

    // طباعة الفاتورة
    printSale(id) {
        // سيتم تطوير هذه الوظيفة لاحقاً
        alert('طباعة الفاتورة #' + id);
    },

    // حذف الفاتورة
    deleteSale(id) {
        if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
            const data = DB.getData();
            const sale = data.sales.find(s => s.id === id);

            if (sale) {
                // إرجاع المخزون
                sale.items.forEach(item => {
                    const productIndex = data.products.findIndex(p => p.id === item.productId);
                    if (productIndex !== -1) {
                        data.products[productIndex].quantity += item.quantity;
                    }
                });

                // تحديث رصيد العميل (للمبيعات الآجلة)
                if (sale.paymentMethod === 'credit') {
                    const customerIndex = data.customers.findIndex(c => c.id === sale.customerId);
                    if (customerIndex !== -1) {
                        data.customers[customerIndex].balance -= sale.grandTotal;
                    }
                }
            }

            data.sales = data.sales.filter(s => s.id !== id);
            DB.saveData(data);
            this.loadSales();
            this.loadProducts();
            alert('تم حذف الفاتورة بنجاح');
        }
    },

    // إنشاء فاتورة جديدة لعميل محدد
    newSaleForCustomer(customerId) {
        this.switchTab('new');
        document.getElementById('sale-customer').value = customerId;
        this.updatePaymentMethod(customerId);
    },

    // عرض نموذج الفاتورة المفصلة
    showDetailedSaleModal() {
        const modal = document.getElementById('detailed-sale-modal');

        // تحميل البيانات
        this.loadDetailedSaleData();

        // مسح النموذج وتعيين القيم الافتراضية
        this.clearDetailedSaleForm();

        // تعيين رقم الفاتورة التلقائي
        this.generateSaleNumber();

        modal.classList.remove('hidden');
    },

    // تحميل البيانات للنموذج المفصل
    loadDetailedSaleData() {
        // تحميل العملاء
        const data = DB.getData();
        const customerSelect = document.getElementById('detailed-sale-customer');
        customerSelect.innerHTML = '<option value="">اختر العميل</option>';

        data.customers.forEach(customer => {
            const option = new Option(customer.name, customer.id);
            customerSelect.appendChild(option);
        });

        // تحميل المنتجات
        const productSelect = document.getElementById('detailed-item-product');
        productSelect.innerHTML = '<option value="">اختر المنتج</option>';

        const availableProducts = data.products.filter(p => p.quantity > 0);
        availableProducts.forEach(product => {
            const option = new Option(`${product.name} (متوفر: ${product.quantity})`, product.id);
            productSelect.appendChild(option);
        });

        // إضافة معالجات الأحداث
        this.attachDetailedSaleEvents();
    },

    // إضافة معالجات أحداث النموذج المفصل
    attachDetailedSaleEvents() {
        // تحديث معلومات العميل عند الاختيار
        document.getElementById('detailed-sale-customer').addEventListener('change', (e) => {
            this.updateDetailedCustomerInfo(e.target.value);
        });

        // تحديث سعر المنتج عند الاختيار
        document.getElementById('detailed-item-product').addEventListener('change', (e) => {
            this.updateDetailedItemPrice(e.target.value);
        });

        // حساب المجاميع عند تغيير القيم
        ['detailed-discount-value', 'detailed-tax-rate', 'detailed-shipping-cost'].forEach(id => {
            document.getElementById(id).addEventListener('input', () => {
                this.calculateDetailedTotals();
            });
        });

        // تحديث تاريخ الاستحقاق عند تغيير شروط الدفع
        document.getElementById('detailed-payment-terms').addEventListener('input', (e) => {
            this.updateDueDate(e.target.value);
        });
    },

    // توليد رقم الفاتورة
    generateSaleNumber() {
        const data = DB.getData();
        const nextId = data.lastIds.sale + 1;
        const saleNumber = `INV${nextId.toString().padStart(6, '0')}`;
        document.getElementById('detailed-sale-number').value = saleNumber;
    },

    // تحديث معلومات العميل
    updateDetailedCustomerInfo(customerId) {
        if (!customerId) {
            document.getElementById('detailed-customer-phone').value = '';
            document.getElementById('detailed-customer-email').value = '';
            document.getElementById('detailed-customer-address').value = '';
            return;
        }

        const data = DB.getData();
        const customer = data.customers.find(c => c.id == customerId);
        if (customer) {
            document.getElementById('detailed-customer-phone').value = customer.phone || '';
            document.getElementById('detailed-customer-email').value = customer.email || '';
            document.getElementById('detailed-customer-address').value = customer.address || '';

            // تحديث شروط الدفع
            if (customer.paymentTerms) {
                document.getElementById('detailed-payment-terms').value = customer.paymentTerms;
                this.updateDueDate(customer.paymentTerms);
            }
        }
    },

    // تحديث سعر المنتج
    updateDetailedItemPrice(productId) {
        if (!productId) {
            document.getElementById('detailed-item-price').value = '';
            return;
        }

        const data = DB.getData();
        const product = data.products.find(p => p.id == productId);
        if (product) {
            document.getElementById('detailed-item-price').value = product.salePrice || 0;
        }
    },

    // تحديث تاريخ الاستحقاق
    updateDueDate(paymentTerms) {
        const saleDate = new Date(document.getElementById('detailed-sale-date').value);
        const dueDate = new Date(saleDate);
        dueDate.setDate(dueDate.getDate() + parseInt(paymentTerms || 0));

        document.getElementById('detailed-due-date').value = dueDate.toISOString().split('T')[0];
    },

    // إضافة صنف للفاتورة المفصلة
    addDetailedItem() {
        const productId = document.getElementById('detailed-item-product').value;
        const quantity = parseFloat(document.getElementById('detailed-item-quantity').value);
        const price = parseFloat(document.getElementById('detailed-item-price').value);
        const discount = parseFloat(document.getElementById('detailed-item-discount').value) || 0;
        const tax = parseFloat(document.getElementById('detailed-item-tax').value) || 0;
        const notes = document.getElementById('detailed-item-notes').value.trim();

        if (!productId || !quantity || !price) {
            alert('يرجى ملء البيانات المطلوبة للصنف');
            return;
        }

        const data = DB.getData();
        const product = data.products.find(p => p.id == productId);

        if (!product) {
            alert('المنتج غير موجود');
            return;
        }

        if (quantity > product.quantity) {
            alert(`الكمية المطلوبة (${quantity}) أكبر من المتوفر (${product.quantity})`);
            return;
        }

        // حساب المجاميع للصنف
        const subtotal = quantity * price;
        const discountAmount = subtotal * (discount / 100);
        const afterDiscount = subtotal - discountAmount;
        const taxAmount = afterDiscount * (tax / 100);
        const total = afterDiscount + taxAmount;

        // إضافة الصنف
        if (!this.detailedSaleItems) {
            this.detailedSaleItems = [];
        }

        const item = {
            productId: parseInt(productId),
            productName: product.name,
            quantity: quantity,
            price: price,
            discount: discount,
            tax: tax,
            subtotal: subtotal,
            discountAmount: discountAmount,
            taxAmount: taxAmount,
            total: total,
            notes: notes
        };

        this.detailedSaleItems.push(item);
        this.renderDetailedSaleItems();
        this.calculateDetailedTotals();
        this.clearDetailedItemForm();
    },

    // عرض أصناف الفاتورة المفصلة
    renderDetailedSaleItems() {
        const tbody = document.getElementById('detailed-sale-items-body');
        tbody.innerHTML = '';

        if (!this.detailedSaleItems || this.detailedSaleItems.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">لا توجد أصناف في الفاتورة</td>
                </tr>
            `;
            return;
        }

        this.detailedSaleItems.forEach((item, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.productName}</td>
                <td>${item.quantity}</td>
                <td>${this.formatCurrency(item.price)}</td>
                <td>${item.discount}%</td>
                <td>${item.tax}%</td>
                <td>${this.formatCurrency(item.total)}</td>
                <td>${item.notes || '-'}</td>
                <td>
                    <button onclick="Sales.removeDetailedItem(${index})" class="btn btn-sm btn-danger">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    },

    // إزالة صنف من الفاتورة المفصلة
    removeDetailedItem(index) {
        if (this.detailedSaleItems && this.detailedSaleItems[index]) {
            this.detailedSaleItems.splice(index, 1);
            this.renderDetailedSaleItems();
            this.calculateDetailedTotals();
        }
    },

    // حساب المجاميع للفاتورة المفصلة
    calculateDetailedTotals() {
        if (!this.detailedSaleItems || this.detailedSaleItems.length === 0) {
            document.getElementById('detailed-subtotal').textContent = '0.00 ريال';
            document.getElementById('detailed-total-discount').textContent = '0.00 ريال';
            document.getElementById('detailed-after-discount').textContent = '0.00 ريال';
            document.getElementById('detailed-total-tax').textContent = '0.00 ريال';
            document.getElementById('detailed-shipping-total').textContent = '0.00 ريال';
            document.getElementById('detailed-grand-total').textContent = '0.00 ريال';
            return;
        }

        // حساب المجموع الفرعي
        const subtotal = this.detailedSaleItems.reduce((sum, item) => sum + item.subtotal, 0);

        // حساب الخصم العام
        const discountType = document.getElementById('detailed-discount-type').value;
        const discountValue = parseFloat(document.getElementById('detailed-discount-value').value) || 0;
        let generalDiscount = 0;

        if (discountType === 'percentage') {
            generalDiscount = subtotal * (discountValue / 100);
        } else {
            generalDiscount = discountValue;
        }

        // إجمالي خصم الأصناف
        const itemsDiscount = this.detailedSaleItems.reduce((sum, item) => sum + item.discountAmount, 0);
        const totalDiscount = itemsDiscount + generalDiscount;

        // المجموع بعد الخصم
        const afterDiscount = subtotal - totalDiscount;

        // حساب الضريبة
        const itemsTax = this.detailedSaleItems.reduce((sum, item) => sum + item.taxAmount, 0);
        const generalTaxRate = parseFloat(document.getElementById('detailed-tax-rate').value) || 0;
        const generalTax = afterDiscount * (generalTaxRate / 100);
        const totalTax = itemsTax + generalTax;

        // تكلفة الشحن
        const shippingCost = parseFloat(document.getElementById('detailed-shipping-cost').value) || 0;

        // المجموع الإجمالي
        const grandTotal = afterDiscount + totalTax + shippingCost;

        // تحديث العرض
        document.getElementById('detailed-subtotal').textContent = this.formatCurrency(subtotal);
        document.getElementById('detailed-total-discount').textContent = this.formatCurrency(totalDiscount);
        document.getElementById('detailed-after-discount').textContent = this.formatCurrency(afterDiscount);
        document.getElementById('detailed-total-tax').textContent = this.formatCurrency(totalTax);
        document.getElementById('detailed-shipping-total').textContent = this.formatCurrency(shippingCost);
        document.getElementById('detailed-grand-total').textContent = this.formatCurrency(grandTotal);
    },

    // مسح نموذج الصنف
    clearDetailedItemForm() {
        document.getElementById('detailed-item-product').value = '';
        document.getElementById('detailed-item-quantity').value = '1';
        document.getElementById('detailed-item-price').value = '';
        document.getElementById('detailed-item-discount').value = '0';
        document.getElementById('detailed-item-tax').value = '15';
        document.getElementById('detailed-item-notes').value = '';
    },

    // حفظ الفاتورة المفصلة
    saveDetailedSale() {
        // التحقق من البيانات المطلوبة
        const customerId = document.getElementById('detailed-sale-customer').value;
        const saleDate = document.getElementById('detailed-sale-date').value;

        if (!customerId) {
            alert('يرجى اختيار العميل');
            return;
        }

        if (!saleDate) {
            alert('يرجى تحديد تاريخ الفاتورة');
            return;
        }

        if (!this.detailedSaleItems || this.detailedSaleItems.length === 0) {
            alert('يرجى إضافة أصناف للفاتورة');
            return;
        }

        // جمع بيانات الفاتورة
        const saleData = {
            id: DB.getNextId('sale'),
            number: document.getElementById('detailed-sale-number').value,
            customerId: parseInt(customerId),
            date: saleDate,
            time: document.getElementById('detailed-sale-time').value,
            reference: document.getElementById('detailed-sale-reference').value,
            type: document.getElementById('detailed-sale-type').value,
            status: document.getElementById('detailed-sale-status').value,
            paymentMethod: document.getElementById('detailed-payment-method').value,
            paymentTerms: parseInt(document.getElementById('detailed-payment-terms').value) || 0,
            dueDate: document.getElementById('detailed-due-date').value,
            shippingMethod: document.getElementById('detailed-shipping-method').value,
            shippingCost: parseFloat(document.getElementById('detailed-shipping-cost').value) || 0,
            deliveryDate: document.getElementById('detailed-delivery-date').value,
            items: [...this.detailedSaleItems],
            discountType: document.getElementById('detailed-discount-type').value,
            discountValue: parseFloat(document.getElementById('detailed-discount-value').value) || 0,
            taxRate: parseFloat(document.getElementById('detailed-tax-rate').value) || 0,
            notes: document.getElementById('detailed-sale-notes').value,
            internalNotes: document.getElementById('detailed-internal-notes').value,
            createdAt: new Date().toISOString()
        };

        // حساب المجاميع النهائية
        this.calculateDetailedTotals();

        // إضافة المجاميع للفاتورة
        const subtotal = this.detailedSaleItems.reduce((sum, item) => sum + item.subtotal, 0);
        const totalDiscount = parseFloat(document.getElementById('detailed-total-discount').textContent.replace(/[^\d.-]/g, ''));
        const totalTax = parseFloat(document.getElementById('detailed-total-tax').textContent.replace(/[^\d.-]/g, ''));
        const grandTotal = parseFloat(document.getElementById('detailed-grand-total').textContent.replace(/[^\d.-]/g, ''));

        saleData.subtotal = subtotal;
        saleData.totalDiscount = totalDiscount;
        saleData.totalTax = totalTax;
        saleData.grandTotal = grandTotal;

        // حفظ الفاتورة
        const data = DB.getData();
        data.sales.push(saleData);

        // تحديث المخزون
        this.detailedSaleItems.forEach(item => {
            const product = data.products.find(p => p.id === item.productId);
            if (product) {
                product.quantity -= item.quantity;
            }
        });

        // تحديث رصيد العميل (إذا كان الدفع آجل)
        if (saleData.paymentMethod === 'credit') {
            const customer = data.customers.find(c => c.id === saleData.customerId);
            if (customer) {
                customer.balance = (customer.balance || 0) + grandTotal;
            }
        }

        DB.saveData(data);
        alert('تم حفظ الفاتورة بنجاح');
        this.closeDetailedSaleModal();
        this.loadSales();
    },

    // حفظ وطباعة الفاتورة المفصلة
    saveAndPrintDetailedSale() {
        this.saveDetailedSale();
        // يمكن إضافة وظيفة الطباعة هنا
        setTimeout(() => {
            window.print();
        }, 500);
    },

    // مسح النموذج المفصل
    clearDetailedSaleForm() {
        const form = document.getElementById('detailed-sale-form');
        form.reset();

        // تعيين القيم الافتراضية
        const today = new Date();
        document.getElementById('detailed-sale-date').value = today.toISOString().split('T')[0];
        document.getElementById('detailed-sale-time').value = today.toTimeString().slice(0, 5);
        document.getElementById('detailed-sale-type').value = 'sale';
        document.getElementById('detailed-sale-status').value = 'confirmed';
        document.getElementById('detailed-payment-method').value = 'cash';
        document.getElementById('detailed-payment-terms').value = '0';
        document.getElementById('detailed-shipping-method').value = 'pickup';
        document.getElementById('detailed-shipping-cost').value = '0';
        document.getElementById('detailed-discount-type').value = 'percentage';
        document.getElementById('detailed-discount-value').value = '0';
        document.getElementById('detailed-tax-rate').value = '15';
        document.getElementById('detailed-item-quantity').value = '1';
        document.getElementById('detailed-item-discount').value = '0';
        document.getElementById('detailed-item-tax').value = '15';

        // مسح الأصناف
        this.detailedSaleItems = [];
        this.renderDetailedSaleItems();
        this.calculateDetailedTotals();
    },

    // إغلاق نموذج الفاتورة المفصلة
    closeDetailedSaleModal() {
        document.getElementById('detailed-sale-modal').classList.add('hidden');
    },

    // تنسيق العملة
    formatCurrency(amount) {
        return App.formatCurrency(amount);
    }
};


