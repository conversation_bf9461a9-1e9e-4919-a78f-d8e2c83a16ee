/**
 * لوحة المعلومات الرئيسية
 */
const Dashboard = {
    // عرض لوحة المعلومات
    show() {
        const content = document.getElementById('page-content');
        content.innerHTML = `
            <div class="dashboard">
                <div class="page-header">
                    <h2><i class="fas fa-tachometer-alt"></i> لوحة المعلومات</h2>
                    <div class="date-info">
                        <span id="current-date"></span>
                    </div>
                </div>

                <!-- بطاقات الإحصائيات السريعة -->
                <div class="stats-grid">
                    <div class="stat-card sales">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-sales">٠</h3>
                            <p>إجمالي المبيعات</p>
                            <small id="sales-period">هذا الشهر</small>
                        </div>
                    </div>

                    <div class="stat-card products">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-products">٠</h3>
                            <p>عدد المنتجات</p>
                            <small id="low-stock-alert">تحذير مخزون منخفض</small>
                        </div>
                    </div>

                    <div class="stat-card customers">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-customers">٠</h3>
                            <p>عدد العملاء</p>
                            <small id="customers-debt">إجمالي الديون</small>
                        </div>
                    </div>

                    <div class="stat-card revenue">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="daily-revenue">٠</h3>
                            <p>إيرادات اليوم</p>
                            <small id="revenue-change">مقارنة بالأمس</small>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية والتقارير -->
                <div class="dashboard-charts">
                    <div class="chart-container">
                        <div class="card">
                            <h3>مبيعات آخر ٧ أيام</h3>
                            <div id="sales-chart" class="chart">
                                <canvas id="salesCanvas" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="chart-container">
                        <div class="card">
                            <h3>أفضل المنتجات مبيعاً</h3>
                            <div id="top-products" class="top-products-list">
                                <!-- سيتم ملؤها ديناميكياً -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التنبيهات والإشعارات -->
                <div class="dashboard-alerts">
                    <div class="card">
                        <h3><i class="fas fa-bell"></i> التنبيهات</h3>
                        <div id="alerts-list" class="alerts-list">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>
                    </div>
                </div>

                <!-- آخر المعاملات -->
                <div class="recent-transactions">
                    <div class="card">
                        <h3><i class="fas fa-history"></i> آخر المعاملات</h3>
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>النوع</th>
                                        <th>الرقم</th>
                                        <th>العميل/المورد</th>
                                        <th>المبلغ</th>
                                        <th>التاريخ</th>
                                    </tr>
                                </thead>
                                <tbody id="recent-transactions-body">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.loadDashboardData();
        this.updateCurrentDate();
        this.drawSalesChart();
    },

    // تحميل بيانات لوحة المعلومات
    loadDashboardData() {
        const data = DB.getData();

        // حساب الإحصائيات
        this.calculateStats(data);

        // تحميل أفضل المنتجات
        this.loadTopProducts(data);

        // تحميل التنبيهات
        this.loadAlerts(data);

        // تحميل آخر المعاملات
        this.loadRecentTransactions(data);
    },

    // حساب الإحصائيات
    calculateStats(data) {
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const yesterday = new Date(startOfDay.getTime() - 24 * 60 * 60 * 1000);

        // إجمالي المبيعات هذا الشهر
        const monthlySales = data.sales
            .filter(sale => new Date(sale.date) >= startOfMonth)
            .reduce((sum, sale) => sum + sale.grandTotal, 0);

        // إيرادات اليوم
        const dailyRevenue = data.sales
            .filter(sale => new Date(sale.date) >= startOfDay)
            .reduce((sum, sale) => sum + sale.grandTotal, 0);

        // إيرادات الأمس
        const yesterdayRevenue = data.sales
            .filter(sale => {
                const saleDate = new Date(sale.date);
                return saleDate >= yesterday && saleDate < startOfDay;
            })
            .reduce((sum, sale) => sum + sale.grandTotal, 0);

        // عدد المنتجات
        const totalProducts = data.products.length;

        // المنتجات منخفضة المخزون
        const lowStockProducts = data.products.filter(product =>
            product.quantity <= (product.minStock || 5)
        ).length;

        // عدد العملاء
        const totalCustomers = data.customers.length - 1; // استثناء العميل الافتراضي

        // إجمالي ديون العملاء
        const totalDebt = data.customers
            .filter(customer => !customer.isDefault)
            .reduce((sum, customer) => sum + Math.max(0, customer.balance), 0);

        // تحديث العرض
        document.getElementById('total-sales').textContent = this.formatCurrency(monthlySales);
        document.getElementById('total-products').textContent = this.formatNumber(totalProducts);
        document.getElementById('total-customers').textContent = this.formatNumber(totalCustomers);
        document.getElementById('daily-revenue').textContent = this.formatCurrency(dailyRevenue);

        // تحديث التفاصيل الإضافية
        if (lowStockProducts > 0) {
            document.getElementById('low-stock-alert').textContent = `${lowStockProducts} منتج منخفض المخزون`;
            document.getElementById('low-stock-alert').style.color = '#e74c3c';
        } else {
            document.getElementById('low-stock-alert').textContent = 'المخزون جيد';
            document.getElementById('low-stock-alert').style.color = '#27ae60';
        }

        document.getElementById('customers-debt').textContent = this.formatCurrency(totalDebt);

        // حساب التغيير في الإيرادات
        const revenueChange = dailyRevenue - yesterdayRevenue;
        const revenueChangePercent = yesterdayRevenue > 0 ? (revenueChange / yesterdayRevenue * 100) : 0;
        const changeText = revenueChange >= 0 ? `+${this.formatCurrency(revenueChange)}` : this.formatCurrency(revenueChange);
        document.getElementById('revenue-change').textContent = changeText;
        document.getElementById('revenue-change').style.color = revenueChange >= 0 ? '#27ae60' : '#e74c3c';
    },

    // تحميل أفضل المنتجات
    loadTopProducts(data) {
        // حساب كمية المبيعات لكل منتج
        const productSales = {};

        data.sales.forEach(sale => {
            sale.items.forEach(item => {
                if (productSales[item.productId]) {
                    productSales[item.productId].quantity += item.quantity;
                    productSales[item.productId].revenue += item.total;
                } else {
                    productSales[item.productId] = {
                        productId: item.productId,
                        productName: item.productName,
                        quantity: item.quantity,
                        revenue: item.total
                    };
                }
            });
        });

        // ترتيب المنتجات حسب الكمية المباعة
        const topProducts = Object.values(productSales)
            .sort((a, b) => b.quantity - a.quantity)
            .slice(0, 5);

        const container = document.getElementById('top-products');
        container.innerHTML = '';

        if (topProducts.length === 0) {
            container.innerHTML = '<p class="no-data">لا توجد مبيعات بعد</p>';
            return;
        }

        topProducts.forEach((product, index) => {
            const item = document.createElement('div');
            item.className = 'top-product-item';
            item.innerHTML = `
                <div class="product-rank">${index + 1}</div>
                <div class="product-info">
                    <div class="product-name">${product.productName}</div>
                    <div class="product-stats">
                        <span>الكمية: ${this.formatNumber(product.quantity)}</span>
                        <span>الإيرادات: ${this.formatCurrency(product.revenue)}</span>
                    </div>
                </div>
            `;
            container.appendChild(item);
        });
    },

    // تحميل التنبيهات
    loadAlerts(data) {
        const alerts = [];

        // تنبيهات المخزون المنخفض
        const lowStockProducts = data.products.filter(product =>
            product.quantity <= (product.minStock || 5)
        );

        lowStockProducts.forEach(product => {
            alerts.push({
                type: 'warning',
                icon: 'fas fa-exclamation-triangle',
                message: `مخزون منخفض: ${product.name} (${product.quantity} متبقي)`,
                action: () => Products.show()
            });
        });

        // تنبيهات الديون المتأخرة
        const highDebtCustomers = data.customers.filter(customer =>
            !customer.isDefault && customer.balance > 1000
        );

        highDebtCustomers.forEach(customer => {
            alerts.push({
                type: 'info',
                icon: 'fas fa-user-clock',
                message: `دين مرتفع: ${customer.name} (${this.formatCurrency(customer.balance)})`,
                action: () => Customers.viewCustomer(customer.id)
            });
        });

        const container = document.getElementById('alerts-list');
        container.innerHTML = '';

        if (alerts.length === 0) {
            container.innerHTML = '<p class="no-alerts">لا توجد تنبيهات</p>';
            return;
        }

        alerts.slice(0, 5).forEach(alert => {
            const item = document.createElement('div');
            item.className = `alert-item ${alert.type}`;
            item.innerHTML = `
                <i class="${alert.icon}"></i>
                <span>${alert.message}</span>
            `;
            if (alert.action) {
                item.style.cursor = 'pointer';
                item.addEventListener('click', alert.action);
            }
            container.appendChild(item);
        });
    },

    // تحميل آخر المعاملات
    loadRecentTransactions(data) {
        const transactions = [];

        // إضافة المبيعات
        data.sales.forEach(sale => {
            const customer = data.customers.find(c => c.id === sale.customerId);
            transactions.push({
                type: 'sale',
                id: sale.id,
                customerSupplier: customer ? customer.name : 'غير محدد',
                amount: sale.grandTotal,
                date: sale.date
            });
        });

        // إضافة المشتريات
        data.purchases.forEach(purchase => {
            const supplier = data.suppliers.find(s => s.id === purchase.supplierId);
            transactions.push({
                type: 'purchase',
                id: purchase.id,
                customerSupplier: supplier ? supplier.name : 'غير محدد',
                amount: purchase.grandTotal,
                date: purchase.date
            });
        });

        // ترتيب المعاملات حسب التاريخ
        transactions.sort((a, b) => new Date(b.date) - new Date(a.date));

        const tbody = document.getElementById('recent-transactions-body');
        tbody.innerHTML = '';

        if (transactions.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center">لا توجد معاملات</td>
                </tr>
            `;
            return;
        }

        transactions.slice(0, 10).forEach(transaction => {
            const row = document.createElement('tr');
            const typeText = transaction.type === 'sale' ? 'مبيعات' : 'مشتريات';
            const typeClass = transaction.type === 'sale' ? 'sale' : 'purchase';

            row.innerHTML = `
                <td><span class="transaction-type ${typeClass}">${typeText}</span></td>
                <td>#${transaction.id}</td>
                <td>${transaction.customerSupplier}</td>
                <td>${this.formatCurrency(transaction.amount)}</td>
                <td>${new Date(transaction.date).toLocaleDateString('ar-SA')}</td>
            `;
            tbody.appendChild(row);
        });
    },

    // رسم مخطط المبيعات
    drawSalesChart() {
        const canvas = document.getElementById('salesCanvas');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const data = DB.getData();

        // حساب مبيعات آخر 7 أيام
        const salesData = [];
        const labels = [];

        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];

            const dailySales = data.sales
                .filter(sale => sale.date.split('T')[0] === dateStr)
                .reduce((sum, sale) => sum + sale.grandTotal, 0);

            salesData.push(dailySales);
            labels.push(date.toLocaleDateString('ar-SA', { weekday: 'short' }));
        }

        // رسم مخطط بسيط
        this.drawSimpleChart(ctx, salesData, labels, canvas.width, canvas.height);
    },

    // رسم مخطط بسيط
    drawSimpleChart(ctx, data, labels, width, height) {
        const padding = 40;
        const chartWidth = width - 2 * padding;
        const chartHeight = height - 2 * padding;

        // مسح الكانفاس
        ctx.clearRect(0, 0, width, height);

        // إعداد الألوان
        ctx.fillStyle = '#3498db';
        ctx.strokeStyle = '#2980b9';
        ctx.lineWidth = 2;

        // حساب القيم
        const maxValue = Math.max(...data, 1);
        const stepX = chartWidth / (data.length - 1);

        // رسم الخطوط الإرشادية
        ctx.strokeStyle = '#ecf0f1';
        ctx.lineWidth = 1;
        for (let i = 0; i <= 4; i++) {
            const y = padding + (chartHeight / 4) * i;
            ctx.beginPath();
            ctx.moveTo(padding, y);
            ctx.lineTo(width - padding, y);
            ctx.stroke();
        }

        // رسم الخط
        ctx.strokeStyle = '#3498db';
        ctx.lineWidth = 3;
        ctx.beginPath();

        data.forEach((value, index) => {
            const x = padding + index * stepX;
            const y = height - padding - (value / maxValue) * chartHeight;

            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });

        ctx.stroke();

        // رسم النقاط
        ctx.fillStyle = '#3498db';
        data.forEach((value, index) => {
            const x = padding + index * stepX;
            const y = height - padding - (value / maxValue) * chartHeight;

            ctx.beginPath();
            ctx.arc(x, y, 4, 0, 2 * Math.PI);
            ctx.fill();
        });

        // رسم التسميات
        ctx.fillStyle = '#2c3e50';
        ctx.font = '12px Tajawal';
        ctx.textAlign = 'center';

        labels.forEach((label, index) => {
            const x = padding + index * stepX;
            ctx.fillText(label, x, height - 10);
        });
    },

    // تحديث التاريخ الحالي
    updateCurrentDate() {
        const now = new Date();
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        };
        const dateStr = now.toLocaleDateString('ar-SA', options);
        document.getElementById('current-date').textContent = dateStr;
    },

    // تنسيق العملة
    formatCurrency(amount) {
        const data = DB.getData();
        const currency = data.settings.currency || 'ريال';
        return `${amount.toFixed(2)} ${currency}`;
    },

    // تنسيق الأرقام
    formatNumber(number) {
        return number.toLocaleString('ar-SA');
    }
};



