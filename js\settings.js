/**
 * وحدة الإعدادات
 */
const Settings = {
    init(container) {
        this.container = container;
        this.render();
        this.loadSettings();
        this.setupEventListeners();
        this.addStyles();
    },
    
    render() {
        this.container.innerHTML = `
            <div class="page-header">
                <h2 class="page-title">إعدادات النظام</h2>
            </div>
            
            <div class="settings-container">
                <div class="settings-tabs">
                    <button class="tab-btn active" data-tab="company">بيانات الشركة</button>
                    <button class="tab-btn" data-tab="tax">الضريبة</button>
                    <button class="tab-btn" data-tab="accounts">دليل الحسابات</button>
                    <button class="tab-btn" data-tab="printing">الطباعة</button>
                    <button class="tab-btn" data-tab="password">كلمة المرور</button>
                    <button class="tab-btn" data-tab="backup">النسخ الاحتياطي</button>
                </div>
                
                <div class="card">
                    <!-- تبويب بيانات الشركة -->
                    <div class="tab-content active" id="company-tab">
                        <h3 class="section-title">بيانات الشركة</h3>
                        <form id="company-form">
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="company-name">اسم الشركة</label>
                                        <input type="text" id="company-name" class="form-control" placeholder="أدخل اسم الشركة">
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="company-phone">رقم الهاتف</label>
                                        <input type="text" id="company-phone" class="form-control" placeholder="أدخل رقم الهاتف">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="company-email">البريد الإلكتروني</label>
                                        <input type="email" id="company-email" class="form-control" placeholder="أدخل البريد الإلكتروني">
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="company-website">الموقع الإلكتروني</label>
                                        <input type="url" id="company-website" class="form-control" placeholder="أدخل الموقع الإلكتروني">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="company-address">العنوان</label>
                                <textarea id="company-address" class="form-control" placeholder="أدخل عنوان الشركة"></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="company-logo">شعار الشركة</label>
                                <div class="logo-container">
                                    <img id="logo-preview" src="" alt="شعار الشركة" class="hidden">
                                    <input type="file" id="company-logo" class="form-control" accept="image/*">
                                    <button type="button" id="remove-logo" class="btn btn-danger hidden">إزالة الشعار</button>
                                </div>
                                <small>* يظهر الشعار في الفواتير وتقارير النظام</small>
                            </div>
                            
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">حفظ بيانات الشركة</button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- تبويب إعدادات الضريبة -->
                    <div class="tab-content" id="tax-tab">
                        <h3 class="section-title">إعدادات الضريبة</h3>
                        <form id="tax-form">
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="tax-rate">نسبة ضريبة القيمة المضافة (%)</label>
                                        <input type="number" id="tax-rate" class="form-control" placeholder="15" min="0" max="100">
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="tax-number">الرقم الضريبي</label>
                                        <input type="text" id="tax-number" class="form-control" placeholder="أدخل الرقم الضريبي">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="tax-included" class="form-checkbox">
                                    <label for="tax-included">الأسعار شاملة الضريبة</label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">حفظ إعدادات الضريبة</button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- تبويب دليل الحسابات -->
                    <div class="tab-content" id="accounts-tab">
                        <h3 class="section-title">دليل الحسابات</h3>
                        <form id="accounts-form">
                            <div class="form-group">
                                <button type="button" id="add-account-btn" class="btn btn-primary">إضافة حساب جديد</button>
                            </div>
                            
                            <div class="table-container">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>رقم الحساب</th>
                                            <th>اسم الحساب</th>
                                            <th>نوع الحساب</th>
                                            <th>الحساب الرئيسي</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="accounts-list">
                                        <!-- سيتم إضافة الحسابات هنا -->
                                    </tbody>
                                </table>
                            </div>
                        </form>
                    </div>
                    
                    <!-- تبويب إعدادات الطباعة -->
                    <div class="tab-content" id="printing-tab">
                        <h3 class="section-title">إعدادات الطباعة</h3>
                        <form id="printing-form">
                            <div class="form-group">
                                <label>حجم الورق</label>
                                <div class="radio-group">
                                    <div class="radio-item">
                                        <input type="radio" id="paper-a4" name="paper-size" value="a4" class="form-radio">
                                        <label for="paper-a4">A4</label>
                                    </div>
                                    <div class="radio-item">
                                        <input type="radio" id="paper-a5" name="paper-size" value="a5" class="form-radio">
                                        <label for="paper-a5">A5</label>
                                    </div>
                                    <div class="radio-item">
                                        <input type="radio" id="paper-thermal" name="paper-size" value="thermal" class="form-radio">
                                        <label for="paper-thermal">ورق حراري (80مم)</label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>محتوى تذييل الفاتورة</label>
                                <textarea id="invoice-footer" class="form-control" placeholder="نص يظهر في أسفل الفاتورة"></textarea>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <div class="checkbox-group">
                                            <input type="checkbox" id="show-logo" class="form-checkbox">
                                            <label for="show-logo">إظهار شعار الشركة في الفواتير</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <div class="checkbox-group">
                                            <input type="checkbox" id="show-qr" class="form-checkbox">
                                            <label for="show-qr">إظهار رمز QR في الفواتير الضريبية</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">حفظ إعدادات الطباعة</button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- تبويب تغيير كلمة المرور -->
                    <div class="tab-content" id="password-tab">
                        <h3 class="section-title">تغيير كلمة المرور</h3>
                        <form id="password-form">
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="current-password">كلمة المرور الحالية</label>
                                        <input type="password" id="current-password" class="form-control" placeholder="أدخل كلمة المرور الحالية">
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="new-password">كلمة المرور الجديدة</label>
                                        <input type="password" id="new-password" class="form-control" placeholder="أدخل كلمة المرور الجديدة">
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="confirm-password">تأكيد كلمة المرور</label>
                                        <input type="password" id="confirm-password" class="form-control" placeholder="أعد إدخال كلمة المرور الجديدة">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <button type="button" id="change-password-btn" class="btn btn-primary">تغيير كلمة المرور</button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- تبويب النسخ الاحتياطي -->
                    <div class="tab-content" id="backup-tab">
                        <h3 class="section-title">النسخ الاحتياطي واستعادة البيانات</h3>
                        <form id="backup-form">
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label>تصدير البيانات</label>
                                        <div class="button-group">
                                            <button type="button" id="export-json" class="btn btn-primary">تصدير بصيغة JSON</button>
                                            <button type="button" id="export-excel" class="btn btn-primary">تصدير بصيغة Excel</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="import-file">استيراد البيانات</label>
                                        <input type="file" id="import-file" class="form-control" accept=".json,.xlsx,.xls">
                                        <button type="button" id="import-btn" class="btn btn-primary">استيراد البيانات</button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="auto-backup" class="form-checkbox">
                                    <label for="auto-backup">تفعيل النسخ الاحتياطي التلقائي</label>
                                </div>
                            </div>
                            
                            <div class="form-group" id="auto-backup-options" style="display: none;">
                                <label>فترة النسخ الاحتياطي التلقائي</label>
                                <div class="radio-group">
                                    <div class="radio-item">
                                        <input type="radio" id="backup-daily" name="backup-interval" value="daily" class="form-radio">
                                        <label for="backup-daily">يومي</label>
                                    </div>
                                    <div class="radio-item">
                                        <input type="radio" id="backup-weekly" name="backup-interval" value="weekly" class="form-radio">
                                        <label for="backup-weekly">أسبوعي</label>
                                    </div>
                                    <div class="radio-item">
                                        <input type="radio" id="backup-monthly" name="backup-interval" value="monthly" class="form-radio">
                                        <label for="backup-monthly">شهري</label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">حفظ إعدادات النسخ الاحتياطي</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- نموذج إضافة/تعديل حساب -->
            <div id="account-modal" class="modal hidden">
                <div class="modal-content">
                    <span class="close-modal">&times;</span>
                    <h3 id="account-modal-title">إضافة حساب جديد</h3>
                    <form id="account-form">
                        <input type="hidden" id="account-id">
                        <div class="form-group">
                            <label for="account-number">رقم الحساب</label>
                            <input type="text" id="account-number" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="account-name">اسم الحساب</label>
                            <input type="text" id="account-name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="account-type">نوع الحساب</label>
                            <select id="account-type" class="form-control" required>
                                <option value="">اختر نوع الحساب</option>
                                <option value="asset">أصول</option>
                                <option value="liability">خصوم</option>
                                <option value="equity">حقوق ملكية</option>
                                <option value="revenue">إيرادات</option>
                                <option value="expense">مصروفات</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="parent-account">الحساب الرئيسي</label>
                            <select id="parent-account" class="form-control">
                                <option value="">بدون حساب رئيسي</option>
                                <!-- سيتم إضافة الحسابات هنا -->
                            </select>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">حفظ الحساب</button>
                        </div>
                    </form>
                </div>
            </div>
        `;
    },
    
    loadSettings() {
        const data = DB.getData();
        const settings = data.settings || {};
        
        // بيانات الشركة
        document.getElementById('company-name').value = settings.companyName || '';
        document.getElementById('company-phone').value = settings.companyPhone || '';
        document.getElementById('company-email').value = settings.companyEmail || '';
        document.getElementById('company-website').value = settings.companyWebsite || '';
        document.getElementById('company-address').value = settings.companyAddress || '';
        
        // عرض الشعار إذا كان موجودًا
        if (settings.companyLogo) {
            document.getElementById('logo-preview').src = settings.companyLogo;
            document.getElementById('logo-preview').classList.remove('hidden');
            document.getElementById('remove-logo').classList.remove('hidden');
        }
        
        // إعدادات الضريبة
        document.getElementById('tax-rate').value = settings.taxRate || 15;
        document.getElementById('tax-included').checked = settings.taxIncluded || false;
        document.getElementById('tax-number').value = settings.taxNumber || '';
        
        // إعدادات الطباعة
        const paperSize = settings.paperSize || 'a4';
        const paperSizeElement = document.querySelector(`input[name="paper-size"][value="${paperSize}"]`);
        if (paperSizeElement) {
            paperSizeElement.checked = true;
        }
        
        document.getElementById('invoice-footer').value = settings.invoiceFooter || '';
        document.getElementById('show-logo').checked = settings.showLogo !== false; // افتراضي true
        document.getElementById('show-qr').checked = settings.showQR || false;
        
        // إعدادات النسخ الاحتياطي
        document.getElementById('auto-backup').checked = settings.autoBackup || false;
        const backupInterval = settings.backupInterval || 'weekly';
        const backupIntervalElement = document.querySelector(`input[name="backup-interval"][value="${backupInterval}"]`);
        if (backupIntervalElement) {
            backupIntervalElement.checked = true;
        }
        
        // إظهار/إخفاء خيارات النسخ الاحتياطي التلقائي
        if (settings.autoBackup) {
            document.getElementById('auto-backup-options').style.display = 'block';
        } else {
            document.getElementById('auto-backup-options').style.display = 'none';
        }
        
        // تحميل دليل الحسابات
        this.loadAccounts();
    },
    
    loadAccounts() {
        const data = DB.getData();
        const accounts = data.accounts || [];
        const accountsList = document.getElementById('accounts-list');
        const parentAccountSelect = document.getElementById('parent-account');
        
        // تفريغ القائمة
        accountsList.innerHTML = '';
        
        // إعادة تعبئة قائمة الحسابات الرئيسية
        parentAccountSelect.innerHTML = '<option value="">بدون حساب رئيسي</option>';
        
        if (accounts.length === 0) {
            accountsList.innerHTML = '<tr><td colspan="5">لا توجد حسابات مضافة</td></tr>';
            return;
        }
        
        // إضافة الحسابات إلى الجدول وقائمة الحسابات الرئيسية
        accounts.forEach(account => {
            // إضافة الحساب إلى الجدول
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${account.number}</td>
                <td>${account.name}</td>
                <td>${this.getAccountTypeName(account.type)}</td>
                <td>${this.getParentAccountName(account.parentId, accounts)}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-primary edit-account" data-id="${account.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-danger delete-account" data-id="${account.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            accountsList.appendChild(row);
            
            // إضافة الحساب إلى قائمة الحسابات الرئيسية
            const option = document.createElement('option');
            option.value = account.id;
            option.textContent = `${account.number} - ${account.name}`;
            parentAccountSelect.appendChild(option);
        });
        
        // إضافة أحداث للأزرار
        document.querySelectorAll('.edit-account').forEach(button => {
            button.addEventListener('click', () => {
                this.editAccount(button.dataset.id);
            });
        });
        
        document.querySelectorAll('.delete-account').forEach(button => {
            button.addEventListener('click', () => {
                this.deleteAccount(button.dataset.id);
            });
        });
    },
    
    getAccountTypeName(type) {
        switch (type) {
            case 'asset': return 'الأصول';
            case 'liability': return 'الخصوم';
            case 'equity': return 'حقوق الملكية';
            case 'revenue': return 'الإيرادات';
            case 'expense': return 'مصروفات';
            default: return 'نوع غير معروف';
        }
    },
    
    getParentAccountName(parentId, accounts) {
        const parentAccount = accounts.find(account => account.id === parentId);
        return parentAccount ? `${parentAccount.number} - ${parentAccount.name}` : 'بدون حساب رئيسي';
    },
    
    setupEventListeners() {
        // معالجة تحميل شعار الشركة
        document.getElementById('company-logo').addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (event) => {
                    document.getElementById('logo-preview').src = event.target.result;
                    document.getElementById('logo-preview').classList.remove('hidden');
                    document.getElementById('remove-logo').classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }
        });
        
        // إزالة شعار الشركة
        document.getElementById('remove-logo').addEventListener('click', () => {
            document.getElementById('company-logo').value = '';
            document.getElementById('logo-preview').src = '';
            document.getElementById('logo-preview').classList.add('hidden');
            document.getElementById('remove-logo').classList.add('hidden');
        });
        
        // إظهار/إخفاء خيارات النسخ الاحتياطي التلقائي
        document.getElementById('auto-backup').addEventListener('change', (e) => {
            if (e.target.checked) {
                document.getElementById('auto-backup-options').style.display = 'block';
            } else {
                document.getElementById('auto-backup-options').style.display = 'none';
            }
        });
        
        // تغيير كلمة المرور
        document.getElementById('change-password-btn').addEventListener('click', () => {
            const currentPassword = document.getElementById('current-password').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            
            if (!currentPassword || !newPassword || !confirmPassword) {
                alert('يرجى ملء جميع حقول كلمة المرور');
                return;
            }
            
            if (newPassword !== confirmPassword) {
                alert('كلمة المرور الجديدة وتأكيدها غير متطابقين');
                return;
            }
            
            if (Auth.changePassword(currentPassword, newPassword)) {
                alert('تم تغيير كلمة المرور بنجاح');
                document.getElementById('current-password').value = '';
                document.getElementById('new-password').value = '';
                document.getElementById('confirm-password').value = '';
            } else {
                alert('كلمة المرور الحالية غير صحيحة');
            }
        });
        
        // تصدير البيانات بصيغة JSON
        document.getElementById('export-json').addEventListener('click', () => {
            DB.exportData();
            alert('تم تصدير البيانات بنجاح');
        });
        
        // تصدير البيانات بصيغة Excel
        document.getElementById('export-excel').addEventListener('click', () => {
            alert('سيتم تنفيذ هذه الميزة قريبًا');
        });
        
        // استيراد البيانات
        document.getElementById('import-btn').addEventListener('click', () => {
            const fileInput = document.getElementById('import-file');
            
            if (fileInput.files.length === 0) {
                alert('الرجاء اختيار ملف للاستيراد');
                return;
            }
            
            const file = fileInput.files[0];
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    if (DB.importData(e.target.result)) {
                        alert('تم استيراد البيانات بنجاح');
                        window.location.reload();
                    } else {
                        alert('حدث خطأ أثناء استيراد البيانات');
                    }
                } catch (error) {
                    alert('الملف غير صالح. يرجى اختيار ملف JSON صحيح');
                }
            };
            
            reader.readAsText(file);
        });
        
        // حفظ جميع الإعدادات
        document.getElementById('settings-form').addEventListener('submit', (e) => {
            e.preventDefault();
            
            const data = DB.getData();
            
            // حفظ بيانات الشركة
            data.settings.companyName = document.getElementById('company-name').value;
            data.settings.companyPhone = document.getElementById('company-phone').value;
            data.settings.companyEmail = document.getElementById('company-email').value;
            data.settings.companyWebsite = document.getElementById('company-website').value;
            data.settings.companyAddress = document.getElementById('company-address').value;
            
            // حفظ شعار الشركة
            const logoPreview = document.getElementById('logo-preview');
            if (!logoPreview.classList.contains('hidden')) {
                data.settings.companyLogo = logoPreview.src;
            } else {
                data.settings.companyLogo = '';
            }
            
            // حفظ إعدادات الضريبة
            data.settings.taxRate = parseFloat(document.getElementById('tax-rate').value) || 15;
            data.settings.taxIncluded = document.getElementById('tax-included').checked;
            data.settings.taxNumber = document.getElementById('tax-number').value;
            
            // حفظ إعدادات الطباعة
            const paperSizeElement = document.querySelector('input[name="paper-size"]:checked');
            data.settings.paperSize = paperSizeElement ? paperSizeElement.value : 'a4';
            data.settings.invoiceFooter = document.getElementById('invoice-footer').value;
            data.settings.showLogo = document.getElementById('show-logo').checked;
            data.settings.showQR = document.getElementById('show-qr').checked;
            
            // حفظ إعدادات النسخ الاحتياطي
            data.settings.autoBackup = document.getElementById('auto-backup').checked;
            const backupIntervalElement = document.querySelector('input[name="backup-interval"]:checked');
            data.settings.backupInterval = backupIntervalElement ? backupIntervalElement.value : 'weekly';
            
            DB.saveData(data);
            
            alert('تم حفظ جميع الإعدادات بنجاح');
        });
    },
    
    // إضافة CSS لصفحة الإعدادات
    addStyles() {
        const styleElement = document.createElement('style');
        styleElement.textContent = `
            .settings-container {
                max-width: 900px;
                margin: 0 auto;
            }
            
            .section-title {
                margin: 15px 0;
                color: var(--primary-color);
                font-size: 1.2rem;
            }
            
            .settings-divider {
                margin: 25px 0;
                border: none;
                border-top: 1px solid #eee;
            }
            
            .logo-container {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-top: 10px;
            }
            
            #logo-preview {
                max-width: 150px;
                max-height: 100px;
                border: 1px solid #ddd;
                padding: 5px;
            }
            
            .button-group {
                display: flex;
                gap: 10px;
                margin-top: 10px;
            }
            
            #auto-backup-options {
                margin-top: 10px;
                padding: 10px;
                background-color: #f9f9f9;
                border-radius: 5px;
            }
            
            .radio-group, .checkbox-group {
                margin-top: 10px;
            }
            
            .radio-item, .checkbox-item {
                display: flex;
                align-items: center;
                gap: 5px;
            }
            
            .form-control {
                padding: 10px 15px;
                border: none;
                border-radius: var(--border-radius);
                background: #f5f7fa;
                box-shadow: inset 2px 2px 5px var(--shadow-color), 









