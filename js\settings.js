/**
 * الإعدادات
 */
const Settings = {
    // عرض صفحة الإعدادات
    show() {
        console.log('Settings.show() called');
        const content = document.getElementById('page-content');
        console.log('Content element:', content);
        content.innerHTML = `
            <div class="page-header">
                <h2><i class="fas fa-cog"></i> الإعدادات</h2>
                <div>
                    <button id="backup-btn" class="btn btn-success">
                        <i class="fas fa-download"></i> نسخة احتياطية
                    </button>
                    <button id="restore-btn" class="btn btn-warning">
                        <i class="fas fa-upload"></i> استعادة
                    </button>
                    <button id="back-to-dashboard-btn" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i> العودة
                    </button>
                </div>
            </div>

            <div class="settings-container">
                <!-- إعدادات الشركة -->
                <div class="card settings-section">
                    <h3><i class="fas fa-building"></i> بيانات الشركة</h3>
                    <form id="company-form">
                        <div class="form-group">
                            <label for="company-name">اسم الشركة</label>
                            <input type="text" id="company-name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="company-address">العنوان</label>
                            <textarea id="company-address" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="company-phone">رقم الهاتف</label>
                            <input type="tel" id="company-phone" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="company-email">البريد الإلكتروني</label>
                            <input type="email" id="company-email" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="company-logo">شعار الشركة</label>
                            <input type="file" id="company-logo" class="form-control" accept="image/*">
                            <div id="logo-preview" class="logo-preview"></div>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ بيانات الشركة
                            </button>
                            <button type="button" id="back-from-company-btn" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة
                            </button>
                        </div>
                    </form>
                </div>

                <!-- الإعدادات المالية -->
                <div class="card settings-section">
                    <h3><i class="fas fa-money-bill"></i> الإعدادات المالية</h3>
                    <form id="financial-form">
                        <div class="form-group">
                            <label for="tax-rate">نسبة الضريبة (%)</label>
                            <input type="number" id="tax-rate" class="form-control" min="0" max="100" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="currency">العملة الأساسية</label>
                            <select id="currency" class="form-control">
                                <optgroup label="العملات العربية">
                                    <option value="ريال سعودي">ريال سعودي (SAR)</option>
                                    <option value="درهم إماراتي">درهم إماراتي (AED)</option>
                                    <option value="دينار كويتي">دينار كويتي (KWD)</option>
                                    <option value="دينار بحريني">دينار بحريني (BHD)</option>
                                    <option value="ريال قطري">ريال قطري (QAR)</option>
                                    <option value="ريال عماني">ريال عماني (OMR)</option>
                                    <option value="جنيه مصري">جنيه مصري (EGP)</option>
                                    <option value="ليرة لبنانية">ليرة لبنانية (LBP)</option>
                                    <option value="ليرة سورية">ليرة سورية (SYP)</option>
                                    <option value="دينار أردني">دينار أردني (JOD)</option>
                                    <option value="دينار عراقي">دينار عراقي (IQD)</option>
                                    <option value="درهم مغربي">درهم مغربي (MAD)</option>
                                    <option value="دينار تونسي">دينار تونسي (TND)</option>
                                    <option value="دينار جزائري">دينار جزائري (DZD)</option>
                                    <option value="جنيه سوداني">جنيه سوداني (SDG)</option>
                                    <option value="ريال يمني">ريال يمني (YER)</option>
                                </optgroup>
                                <optgroup label="العملات العالمية الرئيسية">
                                    <option value="دولار أمريكي">دولار أمريكي (USD)</option>
                                    <option value="يورو">يورو (EUR)</option>
                                    <option value="جنيه إسترليني">جنيه إسترليني (GBP)</option>
                                    <option value="ين ياباني">ين ياباني (JPY)</option>
                                    <option value="فرنك سويسري">فرنك سويسري (CHF)</option>
                                    <option value="دولار كندي">دولار كندي (CAD)</option>
                                    <option value="دولار أسترالي">دولار أسترالي (AUD)</option>
                                    <option value="دولار نيوزيلندي">دولار نيوزيلندي (NZD)</option>
                                </optgroup>
                                <optgroup label="عملات آسيوية">
                                    <option value="يوان صيني">يوان صيني (CNY)</option>
                                    <option value="روبية هندية">روبية هندية (INR)</option>
                                    <option value="وون كوري">وون كوري (KRW)</option>
                                    <option value="روبية إندونيسية">روبية إندونيسية (IDR)</option>
                                    <option value="رينجت ماليزي">رينجت ماليزي (MYR)</option>
                                    <option value="دولار سنغافوري">دولار سنغافوري (SGD)</option>
                                    <option value="بات تايلندي">بات تايلندي (THB)</option>
                                    <option value="دونغ فيتنامي">دونغ فيتنامي (VND)</option>
                                    <option value="بيزو فلبيني">بيزو فلبيني (PHP)</option>
                                    <option value="تاكا بنجلاديشية">تاكا بنجلاديشية (BDT)</option>
                                    <option value="روبية باكستانية">روبية باكستانية (PKR)</option>
                                    <option value="روبية سريلانكية">روبية سريلانكية (LKR)</option>
                                </optgroup>
                                <optgroup label="عملات أوروبية">
                                    <option value="روبل روسي">روبل روسي (RUB)</option>
                                    <option value="ليرة تركية">ليرة تركية (TRY)</option>
                                    <option value="زلوتي بولندي">زلوتي بولندي (PLN)</option>
                                    <option value="كرونة تشيكية">كرونة تشيكية (CZK)</option>
                                    <option value="فورنت مجري">فورنت مجري (HUF)</option>
                                    <option value="كرونة سويدية">كرونة سويدية (SEK)</option>
                                    <option value="كرونة نرويجية">كرونة نرويجية (NOK)</option>
                                    <option value="كرونة دنماركية">كرونة دنماركية (DKK)</option>
                                </optgroup>
                                <optgroup label="عملات أفريقية">
                                    <option value="راند جنوب أفريقي">راند جنوب أفريقي (ZAR)</option>
                                    <option value="نايرا نيجيرية">نايرا نيجيرية (NGN)</option>
                                    <option value="شيلنج كيني">شيلنج كيني (KES)</option>
                                    <option value="بير إثيوبي">بير إثيوبي (ETB)</option>
                                    <option value="سيدي غاني">سيدي غاني (GHS)</option>
                                </optgroup>
                                <optgroup label="عملات أمريكية">
                                    <option value="بيزو مكسيكي">بيزو مكسيكي (MXN)</option>
                                    <option value="ريال برازيلي">ريال برازيلي (BRL)</option>
                                    <option value="بيزو أرجنتيني">بيزو أرجنتيني (ARS)</option>
                                    <option value="بيزو كولومبي">بيزو كولومبي (COP)</option>
                                    <option value="بيزو تشيلي">بيزو تشيلي (CLP)</option>
                                    <option value="سول بيروفي">سول بيروفي (PEN)</option>
                                </optgroup>
                                <optgroup label="عملات رقمية">
                                    <option value="بيتكوين">بيتكوين (BTC)</option>
                                    <option value="إيثريوم">إيثريوم (ETH)</option>
                                    <option value="تيثر">تيثر (USDT)</option>
                                </optgroup>
                            </select>
                            <small class="form-text">اختر العملة الأساسية التي ستظهر في جميع أنحاء النظام</small>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الإعدادات المالية
                            </button>
                            <button type="button" id="back-from-financial-btn" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة
                            </button>
                        </div>
                    </form>
                </div>

                <!-- إعدادات الطباعة -->
                <div class="card settings-section">
                    <h3><i class="fas fa-print"></i> إعدادات الطباعة</h3>
                    <form id="print-form">
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="show-logo"> إظهار الشعار في الفواتير
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="show-company-info"> إظهار بيانات الشركة
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="paper-size">حجم الورق</label>
                            <select id="paper-size" class="form-control">
                                <option value="A4">A4</option>
                                <option value="A5">A5</option>
                                <option value="Letter">Letter</option>
                                <option value="Receipt">إيصال (80mm)</option>
                            </select>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ إعدادات الطباعة
                            </button>
                            <button type="button" id="back-from-print-btn" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة
                            </button>
                        </div>
                    </form>
                </div>

                <!-- إعدادات الأمان -->
                <div class="card settings-section">
                    <h3><i class="fas fa-shield-alt"></i> إعدادات الأمان</h3>
                    <form id="security-form">
                        <div class="form-group">
                            <label for="current-password">كلمة المرور الحالية</label>
                            <input type="password" id="current-password" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="new-password">كلمة المرور الجديدة</label>
                            <input type="password" id="new-password" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="confirm-password">تأكيد كلمة المرور</label>
                            <input type="password" id="confirm-password" class="form-control" required>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-key"></i> تغيير كلمة المرور
                            </button>
                            <button type="button" id="back-from-security-btn" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة
                            </button>
                        </div>
                    </form>
                </div>

                <!-- إعدادات النظام -->
                <div class="card settings-section">
                    <h3><i class="fas fa-desktop"></i> إعدادات النظام</h3>
                    <form id="system-form">
                        <div class="form-group">
                            <label for="theme">المظهر</label>
                            <select id="theme" class="form-control">
                                <option value="light">فاتح</option>
                                <option value="dark">داكن</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="auto-backup"> النسخ الاحتياطي التلقائي
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="backup-frequency">تكرار النسخ الاحتياطي</label>
                            <select id="backup-frequency" class="form-control">
                                <option value="daily">يومي</option>
                                <option value="weekly">أسبوعي</option>
                                <option value="monthly">شهري</option>
                            </select>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ إعدادات النظام
                            </button>
                            <button type="button" id="back-from-system-btn" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة
                            </button>
                        </div>
                    </form>
                </div>

                <!-- إعدادات قاعدة البيانات -->
                <div class="card settings-section">
                    <h3><i class="fas fa-database"></i> إدارة البيانات</h3>
                    <div class="data-management">
                        <div class="form-group">
                            <label>حجم قاعدة البيانات</label>
                            <div class="data-size" id="data-size">جاري الحساب...</div>
                        </div>
                        <div class="form-group">
                            <label>آخر نسخة احتياطية</label>
                            <div class="last-backup" id="last-backup">لا توجد نسخ احتياطية</div>
                        </div>
                        <div class="data-actions">
                            <button id="clear-sales-btn" class="btn btn-warning">
                                <i class="fas fa-trash"></i> مسح بيانات المبيعات
                            </button>
                            <button id="clear-all-btn" class="btn btn-danger">
                                <i class="fas fa-exclamation-triangle"></i> مسح جميع البيانات
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج استعادة النسخة الاحتياطية -->
            <div id="restore-modal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>استعادة نسخة احتياطية</h3>
                        <button id="close-restore-modal" class="btn-close">&times;</button>
                    </div>
                    <div style="padding: 20px;">
                        <div class="form-group">
                            <label for="backup-file">اختر ملف النسخة الاحتياطية</label>
                            <input type="file" id="backup-file" class="form-control" accept=".json">
                        </div>
                        <div class="form-actions">
                            <button id="restore-data-btn" class="btn btn-primary">
                                <i class="fas fa-upload"></i> استعادة البيانات
                            </button>
                            <button id="backup-from-modal-btn" class="btn btn-success">
                                <i class="fas fa-download"></i> نسخة احتياطية
                            </button>
                            <button id="back-from-modal-btn" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.attachEvents();
        this.loadSettings();
        this.calculateDataSize();
    },

    // إضافة الأحداث
    attachEvents() {
        console.log('Settings.attachEvents() called');
        // نماذج الإعدادات
        document.getElementById('company-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCompanySettings();
        });

        document.getElementById('financial-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveFinancialSettings();
        });

        document.getElementById('print-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.savePrintSettings();
        });

        document.getElementById('security-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.changePassword();
        });

        document.getElementById('system-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveSystemSettings();
        });

        // النسخ الاحتياطي والاستعادة
        document.getElementById('backup-btn').addEventListener('click', () => {
            this.createBackup();
        });

        document.getElementById('restore-btn').addEventListener('click', () => {
            this.showRestoreModal();
        });

        document.getElementById('back-to-dashboard-btn').addEventListener('click', () => {
            App.showPage('dashboard');
        });

        document.getElementById('close-restore-modal').addEventListener('click', () => {
            this.hideRestoreModal();
        });

        document.getElementById('restore-data-btn').addEventListener('click', () => {
            this.restoreData();
        });

        document.getElementById('backup-from-modal-btn').addEventListener('click', () => {
            this.createBackup();
        });

        document.getElementById('back-from-modal-btn').addEventListener('click', () => {
            this.hideRestoreModal();
        });

        // إدارة البيانات
        document.getElementById('clear-sales-btn').addEventListener('click', () => {
            this.clearSalesData();
        });

        document.getElementById('clear-all-btn').addEventListener('click', () => {
            this.clearAllData();
        });

        // تحديث المظهر
        document.getElementById('theme').addEventListener('change', (e) => {
            this.updateTheme(e.target.value);
        });

        // رفع الشعار
        document.getElementById('company-logo').addEventListener('change', (e) => {
            this.handleLogoUpload(e);
        });

        // أزرار العودة
        const backFromCompanyBtn = document.getElementById('back-from-company-btn');
        console.log('back-from-company-btn element:', backFromCompanyBtn);
        if (backFromCompanyBtn) {
            backFromCompanyBtn.addEventListener('click', () => {
                console.log('تم النقر على زر العودة من بيانات الشركة');
                App.showPage('dashboard');
            });
        }

        const backFromFinancialBtn = document.getElementById('back-from-financial-btn');
        if (backFromFinancialBtn) {
            backFromFinancialBtn.addEventListener('click', () => {
                console.log('تم النقر على زر العودة من الإعدادات المالية');
                App.showPage('dashboard');
            });
        }

        const backFromPrintBtn = document.getElementById('back-from-print-btn');
        if (backFromPrintBtn) {
            backFromPrintBtn.addEventListener('click', () => {
                console.log('تم النقر على زر العودة من إعدادات الطباعة');
                App.showPage('dashboard');
            });
        }

        const backFromSecurityBtn = document.getElementById('back-from-security-btn');
        if (backFromSecurityBtn) {
            backFromSecurityBtn.addEventListener('click', () => {
                console.log('تم النقر على زر العودة من إعدادات الأمان');
                App.showPage('dashboard');
            });
        }

        const backFromSystemBtn = document.getElementById('back-from-system-btn');
        if (backFromSystemBtn) {
            backFromSystemBtn.addEventListener('click', () => {
                console.log('تم النقر على زر العودة من إعدادات النظام');
                App.showPage('dashboard');
            });
        }
    },

    // تحميل الإعدادات
    loadSettings() {
        const data = DB.getData();
        const settings = data.settings || {};

        // بيانات الشركة
        document.getElementById('company-name').value = settings.companyInfo?.name || '';
        document.getElementById('company-address').value = settings.companyInfo?.address || '';
        document.getElementById('company-phone').value = settings.companyInfo?.phone || '';
        document.getElementById('company-email').value = settings.companyInfo?.email || '';

        // عرض الشعار
        if (settings.companyInfo?.logo) {
            this.displayLogo(settings.companyInfo.logo);
        }

        // الإعدادات المالية
        document.getElementById('tax-rate').value = settings.taxRate || 14;
        document.getElementById('currency').value = settings.currency || 'جنيه مصري';

        // إعدادات الطباعة
        document.getElementById('show-logo').checked = settings.printSettings?.showLogo !== false;
        document.getElementById('show-company-info').checked = settings.printSettings?.showCompanyInfo !== false;
        document.getElementById('paper-size').value = settings.printSettings?.paperSize || 'A4';

        // إعدادات النظام
        document.getElementById('theme').value = settings.theme || 'light';
        document.getElementById('auto-backup').checked = settings.autoBackup || false;
        document.getElementById('backup-frequency').value = settings.backupFrequency || 'weekly';

        // تطبيق المظهر
        this.updateTheme(settings.theme || 'light');
    },

    // حفظ بيانات الشركة
    saveCompanySettings() {
        const data = DB.getData();

        data.settings.companyInfo = {
            ...data.settings.companyInfo,
            name: document.getElementById('company-name').value.trim(),
            address: document.getElementById('company-address').value.trim(),
            phone: document.getElementById('company-phone').value.trim(),
            email: document.getElementById('company-email').value.trim()
        };

        DB.saveData(data);
        alert('تم حفظ بيانات الشركة بنجاح');
    },

    // حفظ الإعدادات المالية
    saveFinancialSettings() {
        const data = DB.getData();

        data.settings.taxRate = parseFloat(document.getElementById('tax-rate').value) || 15;
        data.settings.currency = document.getElementById('currency').value;

        DB.saveData(data);
        alert('تم حفظ الإعدادات المالية بنجاح');
    },

    // حفظ إعدادات الطباعة
    savePrintSettings() {
        const data = DB.getData();

        data.settings.printSettings = {
            ...data.settings.printSettings,
            showLogo: document.getElementById('show-logo').checked,
            showCompanyInfo: document.getElementById('show-company-info').checked,
            paperSize: document.getElementById('paper-size').value
        };

        DB.saveData(data);
        alert('تم حفظ إعدادات الطباعة بنجاح');
    },

    // تغيير كلمة المرور
    changePassword() {
        const currentPassword = document.getElementById('current-password').value;
        const newPassword = document.getElementById('new-password').value;
        const confirmPassword = document.getElementById('confirm-password').value;

        if (!currentPassword || !newPassword || !confirmPassword) {
            alert('يرجى ملء جميع الحقول');
            return;
        }

        if (newPassword !== confirmPassword) {
            alert('كلمة المرور الجديدة وتأكيدها غير متطابقين');
            return;
        }

        if (newPassword.length < 3) {
            alert('كلمة المرور يجب أن تكون 3 أحرف على الأقل');
            return;
        }

        // التحقق من كلمة المرور الحالية
        const data = DB.getData();
        const currentPasswordHash = this.hashPassword(currentPassword);

        if (currentPasswordHash !== data.settings.password) {
            alert('كلمة المرور الحالية غير صحيحة');
            return;
        }

        // تحديث كلمة المرور
        data.settings.password = this.hashPassword(newPassword);
        DB.saveData(data);

        // مسح الحقول
        document.getElementById('current-password').value = '';
        document.getElementById('new-password').value = '';
        document.getElementById('confirm-password').value = '';

        alert('تم تغيير كلمة المرور بنجاح');
    },

    // حفظ إعدادات النظام
    saveSystemSettings() {
        const data = DB.getData();

        data.settings.theme = document.getElementById('theme').value;
        data.settings.autoBackup = document.getElementById('auto-backup').checked;
        data.settings.backupFrequency = document.getElementById('backup-frequency').value;

        DB.saveData(data);
        this.updateTheme(data.settings.theme);
        alert('تم حفظ إعدادات النظام بنجاح');
    },

    // تحديث المظهر
    updateTheme(theme) {
        if (theme === 'dark') {
            document.body.classList.add('dark-theme');
        } else {
            document.body.classList.remove('dark-theme');
        }
    },

    // معالجة رفع الشعار
    handleLogoUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        if (!file.type.startsWith('image/')) {
            alert('يرجى اختيار ملف صورة صالح');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            const logoData = e.target.result;

            // حفظ الشعار في الإعدادات
            const data = DB.getData();
            data.settings.companyInfo = {
                ...data.settings.companyInfo,
                logo: logoData
            };
            DB.saveData(data);

            // عرض الشعار
            this.displayLogo(logoData);
            alert('تم رفع الشعار بنجاح');
        };
        reader.readAsDataURL(file);
    },

    // عرض الشعار
    displayLogo(logoData) {
        const preview = document.getElementById('logo-preview');
        preview.innerHTML = `
            <img src="${logoData}" alt="شعار الشركة" style="max-width: 200px; max-height: 100px; margin-top: 10px;">
            <button type="button" onclick="Settings.removeLogo()" class="btn btn-sm btn-danger" style="margin-top: 10px; display: block;">
                <i class="fas fa-trash"></i> إزالة الشعار
            </button>
        `;
    },

    // إزالة الشعار
    removeLogo() {
        const data = DB.getData();
        if (data.settings.companyInfo) {
            delete data.settings.companyInfo.logo;
        }
        DB.saveData(data);

        document.getElementById('logo-preview').innerHTML = '';
        document.getElementById('company-logo').value = '';
        alert('تم إزالة الشعار بنجاح');
    },

    // إنشاء نسخة احتياطية
    createBackup() {
        DB.backup();
        alert('تم إنشاء النسخة الاحتياطية بنجاح');
    },

    // عرض نموذج الاستعادة
    showRestoreModal() {
        document.getElementById('restore-modal').classList.remove('hidden');
    },

    // إخفاء نموذج الاستعادة
    hideRestoreModal() {
        document.getElementById('restore-modal').classList.add('hidden');
        document.getElementById('backup-file').value = '';
    },

    // استعادة البيانات
    restoreData() {
        const fileInput = document.getElementById('backup-file');
        const file = fileInput.files[0];

        if (!file) {
            alert('يرجى اختيار ملف النسخة الاحتياطية');
            return;
        }

        if (confirm('هل أنت متأكد من استعادة البيانات؟ سيتم استبدال جميع البيانات الحالية.')) {
            DB.restore(file)
                .then(() => {
                    alert('تم استعادة البيانات بنجاح');
                    this.hideRestoreModal();
                    location.reload();
                })
                .catch((error) => {
                    alert('خطأ في استعادة البيانات: ' + error);
                });
        }
    },

    // تشفير كلمة المرور
    hashPassword(password) {
        // تشفير بسيط - في التطبيق الحقيقي يجب استخدام تشفير أقوى
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // تحويل إلى 32bit integer
        }
        return Math.abs(hash).toString(16);
    },

    // حساب حجم البيانات
    calculateDataSize() {
        const data = DB.getData();
        const dataString = JSON.stringify(data);
        const sizeInBytes = new Blob([dataString]).size;
        const sizeInKB = (sizeInBytes / 1024).toFixed(2);
        const sizeInMB = (sizeInKB / 1024).toFixed(2);

        let displaySize;
        if (sizeInMB >= 1) {
            displaySize = `${sizeInMB} ميجابايت`;
        } else {
            displaySize = `${sizeInKB} كيلوبايت`;
        }

        document.getElementById('data-size').textContent = displaySize;
    },

    // مسح بيانات المبيعات
    clearSalesData() {
        if (confirm('هل أنت متأكد من مسح جميع بيانات المبيعات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            const data = DB.getData();
            data.sales = [];
            DB.saveData(data);
            alert('تم مسح بيانات المبيعات بنجاح');
            this.calculateDataSize();
        }
    },

    // مسح جميع البيانات
    clearAllData() {
        if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            if (confirm('تأكيد أخير: سيتم مسح جميع البيانات نهائياً!')) {
                DB.clearAll();
                alert('تم مسح جميع البيانات بنجاح');
                location.reload();
            }
        }
    }
};
