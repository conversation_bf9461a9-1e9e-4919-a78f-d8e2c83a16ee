/**
 * نظام المشتريات
 */
const Purchases = {
    currentPurchase: {
        items: [],
        supplierId: null,
        total: 0,
        tax: 0,
        grandTotal: 0
    },

    // عرض صفحة المشتريات
    show() {
        const content = document.getElementById('page-content');
        content.innerHTML = `
            <div class="page-header">
                <h2><i class="fas fa-shopping-bag"></i> إدارة المشتريات</h2>
                <button id="new-purchase-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> فاتورة مشتريات جديدة
                </button>
            </div>

            <div class="purchases-tabs">
                <button class="tab-btn active" data-tab="list">قائمة المشتريات</button>
                <button class="tab-btn" data-tab="new">فاتورة جديدة</button>
            </div>

            <!-- قائمة المشتريات -->
            <div id="purchases-list-tab" class="tab-content active">
                <div class="card">
                    <div class="search-bar">
                        <input type="text" id="purchase-search" class="form-control" placeholder="البحث في المشتريات...">
                        <select id="supplier-filter" class="form-control">
                            <option value="">جميع الموردين</option>
                        </select>
                    </div>
                    
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>المورد</th>
                                    <th>التاريخ</th>
                                    <th>المجموع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="purchases-table-body">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- فاتورة جديدة -->
            <div id="new-purchase-tab" class="tab-content">
                <div class="purchase-form-container">
                    <div class="card">
                        <h3>فاتورة مشتريات جديدة</h3>
                        
                        <div class="purchase-header">
                            <div class="form-group">
                                <label for="purchase-supplier">المورد *</label>
                                <select id="purchase-supplier" class="form-control" required>
                                    <option value="">اختر المورد</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="purchase-date">تاريخ الفاتورة</label>
                                <input type="date" id="purchase-date" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="purchase-reference">رقم المرجع</label>
                                <input type="text" id="purchase-reference" class="form-control" placeholder="رقم الفاتورة من المورد">
                            </div>
                        </div>

                        <div class="purchase-items">
                            <h4>أصناف الفاتورة</h4>
                            <div class="add-item-form">
                                <select id="item-product" class="form-control">
                                    <option value="">اختر المنتج</option>
                                </select>
                                <input type="number" id="item-quantity" class="form-control" placeholder="الكمية" min="1">
                                <input type="number" id="item-price" class="form-control" placeholder="سعر الوحدة" step="0.01">
                                <button id="add-item-btn" class="btn btn-secondary">إضافة</button>
                            </div>

                            <div class="items-table">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>السعر</th>
                                            <th>المجموع</th>
                                            <th>إجراء</th>
                                        </tr>
                                    </thead>
                                    <tbody id="purchase-items-body">
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="purchase-totals">
                            <div class="totals-grid">
                                <div class="total-item">
                                    <label>المجموع الفرعي:</label>
                                    <span id="subtotal">٠.٠٠ ريال</span>
                                </div>
                                <div class="total-item">
                                    <label>الضريبة (<span id="tax-rate">١٥</span>%):</label>
                                    <span id="tax-amount">٠.٠٠ ريال</span>
                                </div>
                                <div class="total-item grand-total">
                                    <label>المجموع الإجمالي:</label>
                                    <span id="grand-total">٠.٠٠ ريال</span>
                                </div>
                            </div>
                        </div>

                        <div class="purchase-actions">
                            <button id="save-purchase-btn" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الفاتورة
                            </button>
                            <button id="edit-purchase-btn" class="btn btn-warning">
                                <i class="fas fa-edit"></i> تعديل الفاتورة
                            </button>
                            <button id="clear-purchase-btn" class="btn btn-info">
                                <i class="fas fa-eraser"></i> مسح
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.attachEvents();
        this.loadPurchases();
        this.loadSuppliers();
        this.loadProducts();
        this.setCurrentDate();
    },

    // إضافة الأحداث
    attachEvents() {
        // تبديل التبويبات
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // زر فاتورة جديدة
        document.getElementById('new-purchase-btn').addEventListener('click', () => {
            this.switchTab('new');
        });

        // البحث والفلترة
        document.getElementById('purchase-search').addEventListener('input', (e) => {
            this.searchPurchases(e.target.value);
        });

        document.getElementById('supplier-filter').addEventListener('change', (e) => {
            this.filterBySupplier(e.target.value);
        });

        // إضافة صنف للفاتورة
        document.getElementById('add-item-btn').addEventListener('click', () => {
            this.addItemToPurchase();
        });

        // حفظ الفاتورة
        document.getElementById('save-purchase-btn').addEventListener('click', () => {
            this.savePurchase();
        });

        // مسح الفاتورة
        document.getElementById('clear-purchase-btn').addEventListener('click', () => {
            this.clearPurchase();
        });

        // تحديث السعر عند اختيار المنتج
        document.getElementById('item-product').addEventListener('change', (e) => {
            this.updateItemPrice(e.target.value);
        });
    },

    // تبديل التبويبات
    switchTab(tab) {
        // إزالة الفئة النشطة من جميع التبويبات
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

        // إضافة الفئة النشطة للتبويب المحدد
        document.querySelector(`[data-tab="${tab}"]`).classList.add('active');
        document.getElementById(`${tab === 'list' ? 'purchases-list' : 'new-purchase'}-tab`).classList.add('active');
    },

    // تحميل المشتريات
    loadPurchases() {
        const data = DB.getData();
        const purchases = data.purchases || [];
        this.renderPurchases(purchases);
    },

    // عرض المشتريات في الجدول
    renderPurchases(purchases) {
        const tbody = document.getElementById('purchases-table-body');
        tbody.innerHTML = '';

        if (purchases.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center">لا توجد فواتير مشتريات</td>
                </tr>
            `;
            return;
        }

        const data = DB.getData();
        purchases.forEach(purchase => {
            const supplier = data.suppliers.find(s => s.id === purchase.supplierId);
            const row = document.createElement('tr');
            
            row.innerHTML = `
                <td>#${purchase.id}</td>
                <td>${supplier ? supplier.name : 'غير محدد'}</td>
                <td>${new Date(purchase.date).toLocaleDateString('ar-SA')}</td>
                <td>${this.formatCurrency(purchase.grandTotal)}</td>
                <td><span class="status ${purchase.status}">${this.getStatusText(purchase.status)}</span></td>
                <td class="actions">
                    <button onclick="Purchases.viewPurchase(${purchase.id})" class="btn btn-sm btn-info">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button onclick="Purchases.printPurchase(${purchase.id})" class="btn btn-sm btn-secondary">
                        <i class="fas fa-print"></i>
                    </button>
                    <button onclick="Purchases.deletePurchase(${purchase.id})" class="btn btn-sm btn-danger">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    },

    // تحميل الموردين
    loadSuppliers() {
        const data = DB.getData();
        const suppliers = data.suppliers || [];
        
        const supplierSelect = document.getElementById('purchase-supplier');
        const supplierFilter = document.getElementById('supplier-filter');
        
        supplierSelect.innerHTML = '<option value="">اختر المورد</option>';
        supplierFilter.innerHTML = '<option value="">جميع الموردين</option>';
        
        suppliers.forEach(supplier => {
            const option1 = new Option(supplier.name, supplier.id);
            const option2 = new Option(supplier.name, supplier.id);
            supplierSelect.appendChild(option1);
            supplierFilter.appendChild(option2);
        });
    },

    // تحميل المنتجات
    loadProducts() {
        const data = DB.getData();
        const products = data.products || [];
        
        const productSelect = document.getElementById('item-product');
        productSelect.innerHTML = '<option value="">اختر المنتج</option>';
        
        products.forEach(product => {
            const option = new Option(product.name, product.id);
            productSelect.appendChild(option);
        });
    },

    // تعيين التاريخ الحالي
    setCurrentDate() {
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('purchase-date').value = today;
    },

    // تحديث سعر الصنف
    updateItemPrice(productId) {
        if (!productId) return;
        
        const data = DB.getData();
        const product = data.products.find(p => p.id == productId);
        if (product) {
            document.getElementById('item-price').value = product.cost || product.price || 0;
        }
    },

    // إضافة صنف للفاتورة
    addItemToPurchase() {
        const productId = document.getElementById('item-product').value;
        const quantity = parseFloat(document.getElementById('item-quantity').value);
        const price = parseFloat(document.getElementById('item-price').value);

        if (!productId || !quantity || !price) {
            alert('يرجى ملء جميع بيانات الصنف');
            return;
        }

        const data = DB.getData();
        const product = data.products.find(p => p.id == productId);
        
        if (!product) {
            alert('المنتج غير موجود');
            return;
        }

        const item = {
            productId: parseInt(productId),
            productName: product.name,
            quantity: quantity,
            price: price,
            total: quantity * price
        };

        this.currentPurchase.items.push(item);
        this.renderPurchaseItems();
        this.calculateTotals();
        this.clearItemForm();
    },

    // عرض أصناف الفاتورة
    renderPurchaseItems() {
        const tbody = document.getElementById('purchase-items-body');
        tbody.innerHTML = '';

        this.currentPurchase.items.forEach((item, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.productName}</td>
                <td>${item.quantity}</td>
                <td>${this.formatCurrency(item.price)}</td>
                <td>${this.formatCurrency(item.total)}</td>
                <td>
                    <button onclick="Purchases.removeItem(${index})" class="btn btn-sm btn-danger">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    },

    // إزالة صنف من الفاتورة
    removeItem(index) {
        this.currentPurchase.items.splice(index, 1);
        this.renderPurchaseItems();
        this.calculateTotals();
    },

    // حساب المجاميع
    calculateTotals() {
        const subtotal = this.currentPurchase.items.reduce((sum, item) => sum + item.total, 0);
        const data = DB.getData();
        const taxRate = data.settings.taxRate || 15;
        const tax = subtotal * (taxRate / 100);
        const grandTotal = subtotal + tax;

        this.currentPurchase.total = subtotal;
        this.currentPurchase.tax = tax;
        this.currentPurchase.grandTotal = grandTotal;

        document.getElementById('subtotal').textContent = this.formatCurrency(subtotal);
        document.getElementById('tax-rate').textContent = taxRate;
        document.getElementById('tax-amount').textContent = this.formatCurrency(tax);
        document.getElementById('grand-total').textContent = this.formatCurrency(grandTotal);
    },

    // مسح نموذج الصنف
    clearItemForm() {
        document.getElementById('item-product').value = '';
        document.getElementById('item-quantity').value = '';
        document.getElementById('item-price').value = '';
    },

    // حفظ الفاتورة
    savePurchase() {
        const supplierId = document.getElementById('purchase-supplier').value;
        const date = document.getElementById('purchase-date').value;
        const reference = document.getElementById('purchase-reference').value;

        if (!supplierId) {
            alert('يرجى اختيار المورد');
            return;
        }

        if (this.currentPurchase.items.length === 0) {
            alert('يرجى إضافة أصناف للفاتورة');
            return;
        }

        const data = DB.getData();
        const purchase = {
            id: DB.getNextId('purchase'),
            supplierId: parseInt(supplierId),
            date: date,
            reference: reference,
            items: [...this.currentPurchase.items],
            total: this.currentPurchase.total,
            tax: this.currentPurchase.tax,
            grandTotal: this.currentPurchase.grandTotal,
            status: 'completed',
            createdAt: new Date().toISOString()
        };

        // إضافة الفاتورة
        data.purchases.push(purchase);

        // تحديث المخزون
        purchase.items.forEach(item => {
            const productIndex = data.products.findIndex(p => p.id === item.productId);
            if (productIndex !== -1) {
                data.products[productIndex].quantity += item.quantity;
            }
        });

        // تحديث رصيد المورد
        const supplierIndex = data.suppliers.findIndex(s => s.id === parseInt(supplierId));
        if (supplierIndex !== -1) {
            data.suppliers[supplierIndex].balance += purchase.grandTotal;
        }

        DB.saveData(data);
        this.clearPurchase();
        this.loadPurchases();
        this.switchTab('list');
        
        alert('تم حفظ فاتورة المشتريات بنجاح');
    },

    // مسح الفاتورة
    clearPurchase() {
        this.currentPurchase = {
            items: [],
            supplierId: null,
            total: 0,
            tax: 0,
            grandTotal: 0
        };

        document.getElementById('purchase-supplier').value = '';
        document.getElementById('purchase-reference').value = '';
        this.setCurrentDate();
        this.clearItemForm();
        this.renderPurchaseItems();
        this.calculateTotals();
    },

    // البحث في المشتريات
    searchPurchases(query) {
        const purchases = DB.search('purchases', query, ['reference']);
        this.renderPurchases(purchases);
    },

    // فلترة حسب المورد
    filterBySupplier(supplierId) {
        const data = DB.getData();
        let purchases = data.purchases || [];
        
        if (supplierId) {
            purchases = purchases.filter(p => p.supplierId == supplierId);
        }
        
        this.renderPurchases(purchases);
    },

    // عرض تفاصيل الفاتورة
    viewPurchase(id) {
        // سيتم تطوير هذه الوظيفة لاحقاً
        alert('عرض تفاصيل الفاتورة #' + id);
    },

    // طباعة الفاتورة
    printPurchase(id) {
        // سيتم تطوير هذه الوظيفة لاحقاً
        alert('طباعة الفاتورة #' + id);
    },

    // حذف الفاتورة
    deletePurchase(id) {
        if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
            const data = DB.getData();
            data.purchases = data.purchases.filter(p => p.id !== id);
            DB.saveData(data);
            this.loadPurchases();
            alert('تم حذف الفاتورة بنجاح');
        }
    },

    // الحصول على نص الحالة
    getStatusText(status) {
        const statusMap = {
            'completed': 'مكتملة',
            'pending': 'معلقة',
            'cancelled': 'ملغية'
        };
        return statusMap[status] || status;
    },

    // تنسيق العملة
    formatCurrency(amount) {
        return App.formatCurrency(amount);
    }
};
