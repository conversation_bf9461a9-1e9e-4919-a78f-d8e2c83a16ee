/**
 * إدارة الموردين
 */
const Suppliers = {
    // عرض صفحة الموردين
    show() {
        const content = document.getElementById('page-content');
        content.innerHTML = `
            <div class="page-header">
                <h2><i class="fas fa-truck"></i> إدارة الموردين</h2>
                <div>
                    <button id="add-supplier-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة مورد سريع
                    </button>
                    <button id="add-detailed-supplier-btn" class="btn btn-success">
                        <i class="fas fa-truck-loading"></i> إضافة مورد بكامل البيانات
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="search-bar">
                    <input type="text" id="supplier-search" class="form-control" placeholder="البحث عن مورد...">
                </div>
                
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>اسم المورد</th>
                                <th>الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>الرصيد</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="suppliers-table-body">
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- نموذج إضافة/تعديل المورد -->
            <div id="supplier-modal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="modal-title">إضافة مورد جديد</h3>
                        <button id="close-modal" class="btn-close" onclick="closeModal('supplier-modal')">&times;</button>
                    </div>
                    <form id="supplier-form">
                        <div class="form-group">
                            <label for="supplier-name">اسم المورد *</label>
                            <input type="text" id="supplier-name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="supplier-phone">رقم الهاتف</label>
                            <input type="tel" id="supplier-phone" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="supplier-email">البريد الإلكتروني</label>
                            <input type="email" id="supplier-email" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="supplier-address">العنوان</label>
                            <textarea id="supplier-address" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="supplier-contact-person">الشخص المسؤول</label>
                            <input type="text" id="supplier-contact-person" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="supplier-balance">الرصيد الابتدائي</label>
                            <input type="number" id="supplier-balance" class="form-control" value="0" step="0.01">
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">حفظ</button>
                            <button type="button" id="cancel-btn" class="btn btn-secondary" onclick="closeModal('supplier-modal')">إلغاء</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- نموذج عرض تفاصيل المورد -->
            <div id="supplier-details-modal" class="modal hidden">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3>تفاصيل المورد</h3>
                        <button id="close-details-modal" class="btn-close" onclick="closeModal('supplier-details-modal')">&times;</button>
                    </div>
                    <div id="supplier-details-content"></div>
                </div>
            </div>

            <!-- نموذج إضافة مورد بكامل البيانات -->
            <div id="detailed-supplier-modal" class="modal hidden">
                <div class="modal-content extra-large">
                    <div class="modal-header">
                        <h3>إضافة مورد بكامل البيانات</h3>
                        <button id="close-detailed-supplier-modal" class="btn-close" onclick="Suppliers.closeDetailedSupplierModal()">&times;</button>
                    </div>
                    <form id="detailed-supplier-form" class="detailed-supplier-form">
                        <!-- معلومات أساسية -->
                        <div class="form-section">
                            <h4><i class="fas fa-building"></i> المعلومات الأساسية</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-supplier-code">كود المورد</label>
                                    <input type="text" id="detailed-supplier-code" class="form-control">
                                    <button type="button" onclick="Suppliers.generateSupplierCode()" class="btn btn-sm btn-secondary">توليد تلقائي</button>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-name">اسم المورد *</label>
                                    <input type="text" id="detailed-supplier-name" class="form-control" required>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-name-en">الاسم بالإنجليزية</label>
                                    <input type="text" id="detailed-supplier-name-en" class="form-control">
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-supplier-type">نوع المورد</label>
                                    <select id="detailed-supplier-type" class="form-control">
                                        <option value="company">شركة</option>
                                        <option value="individual">فرد</option>
                                        <option value="factory">مصنع</option>
                                        <option value="distributor">موزع</option>
                                        <option value="wholesaler">تاجر جملة</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-category">فئة المورد</label>
                                    <select id="detailed-supplier-category" class="form-control">
                                        <option value="local">محلي</option>
                                        <option value="international">دولي</option>
                                        <option value="preferred">مفضل</option>
                                        <option value="certified">معتمد</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-status">حالة المورد</label>
                                    <select id="detailed-supplier-status" class="form-control">
                                        <option value="active">نشط</option>
                                        <option value="inactive">غير نشط</option>
                                        <option value="blocked">محظور</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الاتصال -->
                        <div class="form-section">
                            <h4><i class="fas fa-phone"></i> معلومات الاتصال</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-supplier-phone">رقم الهاتف الأساسي</label>
                                    <input type="tel" id="detailed-supplier-phone" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-phone2">رقم هاتف إضافي</label>
                                    <input type="tel" id="detailed-supplier-phone2" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-mobile">رقم الجوال</label>
                                    <input type="tel" id="detailed-supplier-mobile" class="form-control">
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-supplier-email">البريد الإلكتروني</label>
                                    <input type="email" id="detailed-supplier-email" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-website">الموقع الإلكتروني</label>
                                    <input type="url" id="detailed-supplier-website" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-fax">رقم الفاكس</label>
                                    <input type="tel" id="detailed-supplier-fax" class="form-control">
                                </div>
                            </div>
                        </div>

                        <!-- الشخص المسؤول -->
                        <div class="form-section">
                            <h4><i class="fas fa-user-tie"></i> الشخص المسؤول</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-contact-name">اسم الشخص المسؤول</label>
                                    <input type="text" id="detailed-contact-name" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-contact-position">المنصب</label>
                                    <input type="text" id="detailed-contact-position" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-contact-phone">هاتف الشخص المسؤول</label>
                                    <input type="tel" id="detailed-contact-phone" class="form-control">
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-contact-email">بريد الشخص المسؤول</label>
                                    <input type="email" id="detailed-contact-email" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-contact-department">القسم</label>
                                    <input type="text" id="detailed-contact-department" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-contact-notes">ملاحظات عن الشخص المسؤول</label>
                                    <input type="text" id="detailed-contact-notes" class="form-control">
                                </div>
                            </div>
                        </div>

                        <!-- العنوان -->
                        <div class="form-section">
                            <h4><i class="fas fa-map-marker-alt"></i> العنوان</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-supplier-country">الدولة</label>
                                    <input type="text" id="detailed-supplier-country" class="form-control" value="المملكة العربية السعودية">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-city">المدينة</label>
                                    <input type="text" id="detailed-supplier-city" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-district">الحي</label>
                                    <input type="text" id="detailed-supplier-district" class="form-control">
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-supplier-street">الشارع</label>
                                    <input type="text" id="detailed-supplier-street" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-building">رقم المبنى</label>
                                    <input type="text" id="detailed-supplier-building" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-postal">الرمز البريدي</label>
                                    <input type="text" id="detailed-supplier-postal" class="form-control">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="detailed-supplier-address">العنوان التفصيلي</label>
                                <textarea id="detailed-supplier-address" class="form-control" rows="3"></textarea>
                            </div>
                        </div>

                        <!-- المعلومات المالية -->
                        <div class="form-section">
                            <h4><i class="fas fa-money-bill-wave"></i> المعلومات المالية</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-supplier-balance">الرصيد الابتدائي</label>
                                    <input type="number" id="detailed-supplier-balance" class="form-control" value="0" step="0.01">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-credit-limit">الحد الائتماني</label>
                                    <input type="number" id="detailed-supplier-credit-limit" class="form-control" value="0" step="0.01">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-payment-terms">شروط الدفع (أيام)</label>
                                    <input type="number" id="detailed-supplier-payment-terms" class="form-control" value="30" min="0">
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-supplier-discount">خصم افتراضي (%)</label>
                                    <input type="number" id="detailed-supplier-discount" class="form-control" value="0" step="0.01" min="0" max="100">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-tax-number">الرقم الضريبي</label>
                                    <input type="text" id="detailed-supplier-tax-number" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-currency">العملة المفضلة</label>
                                    <select id="detailed-supplier-currency" class="form-control">
                                        <option value="SAR">ريال سعودي</option>
                                        <option value="USD">دولار أمريكي</option>
                                        <option value="EUR">يورو</option>
                                        <option value="AED">درهم إماراتي</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="form-section">
                            <h4><i class="fas fa-info-circle"></i> معلومات إضافية</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-supplier-established">تاريخ التأسيس</label>
                                    <input type="date" id="detailed-supplier-established" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-registration">رقم السجل التجاري</label>
                                    <input type="text" id="detailed-supplier-registration" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-supplier-rating">التقييم</label>
                                    <select id="detailed-supplier-rating" class="form-control">
                                        <option value="">غير محدد</option>
                                        <option value="5">ممتاز (5 نجوم)</option>
                                        <option value="4">جيد جداً (4 نجوم)</option>
                                        <option value="3">جيد (3 نجوم)</option>
                                        <option value="2">مقبول (نجمتان)</option>
                                        <option value="1">ضعيف (نجمة واحدة)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="detailed-supplier-notes">ملاحظات</label>
                                <textarea id="detailed-supplier-notes" class="form-control" rows="3"></textarea>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ المورد
                            </button>
                            <button type="button" onclick="Suppliers.closeDetailedSupplierModal()" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                            <button type="button" onclick="Suppliers.clearDetailedSupplierForm()" class="btn btn-warning">
                                <i class="fas fa-eraser"></i> مسح النموذج
                            </button>
                            <button type="button" onclick="Suppliers.saveAndAddAnotherSupplier()" class="btn btn-info">
                                <i class="fas fa-plus"></i> حفظ وإضافة آخر
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        this.attachEvents();
        this.loadSuppliers();
    },

    // إضافة الأحداث
    attachEvents() {
        // زر إضافة مورد جديد (سريع)
        document.getElementById('add-supplier-btn').addEventListener('click', () => {
            this.showSupplierModal();
        });

        // زر إضافة مورد بكامل البيانات
        document.getElementById('add-detailed-supplier-btn').addEventListener('click', () => {
            this.showDetailedSupplierModal();
        });

        // البحث
        document.getElementById('supplier-search').addEventListener('input', (e) => {
            this.searchSuppliers(e.target.value);
        });

        // نموذج المورد
        document.getElementById('supplier-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveSupplier();
        });

        // نموذج المورد المفصل
        document.getElementById('detailed-supplier-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveDetailedSupplier();
        });

        // لا حاجة لمعالجات إضافية - يتم التعامل معها في النظام العام
    },

    // تحميل الموردين
    loadSuppliers() {
        const data = DB.getData();
        const suppliers = data.suppliers || [];
        this.renderSuppliers(suppliers);
    },

    // عرض الموردين في الجدول
    renderSuppliers(suppliers) {
        const tbody = document.getElementById('suppliers-table-body');
        tbody.innerHTML = '';

        if (suppliers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center">لا توجد موردين مسجلين</td>
                </tr>
            `;
            return;
        }

        suppliers.forEach(supplier => {
            const row = document.createElement('tr');
            const balanceClass = supplier.balance > 0 ? 'negative' : supplier.balance < 0 ? 'positive' : '';
            
            row.innerHTML = `
                <td>${supplier.name}</td>
                <td>${supplier.phone || '-'}</td>
                <td>${supplier.email || '-'}</td>
                <td class="balance ${balanceClass}">${this.formatCurrency(supplier.balance)}</td>
                <td class="actions">
                    <button onclick="Suppliers.viewSupplier(${supplier.id})" class="btn btn-sm btn-info">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button onclick="Suppliers.editSupplier(${supplier.id})" class="btn btn-sm btn-warning">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="Suppliers.deleteSupplier(${supplier.id})" class="btn btn-sm btn-danger">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    },

    // البحث عن الموردين
    searchSuppliers(query) {
        const suppliers = DB.search('suppliers', query, ['name', 'phone', 'email', 'contactPerson']);
        this.renderSuppliers(suppliers);
    },

    // عرض نموذج المورد
    showSupplierModal(supplier = null) {
        const modal = document.getElementById('supplier-modal');
        const title = document.getElementById('modal-title');
        const form = document.getElementById('supplier-form');

        if (supplier) {
            title.textContent = 'تعديل المورد';
            document.getElementById('supplier-name').value = supplier.name;
            document.getElementById('supplier-phone').value = supplier.phone || '';
            document.getElementById('supplier-email').value = supplier.email || '';
            document.getElementById('supplier-address').value = supplier.address || '';
            document.getElementById('supplier-contact-person').value = supplier.contactPerson || '';
            document.getElementById('supplier-balance').value = supplier.balance || 0;
            form.dataset.supplierId = supplier.id;
        } else {
            title.textContent = 'إضافة مورد جديد';
            form.reset();
            delete form.dataset.supplierId;
        }

        modal.classList.remove('hidden');
    },

    // إخفاء نموذج المورد
    hideSupplierModal() {
        document.getElementById('supplier-modal').classList.add('hidden');
    },

    // حفظ المورد
    saveSupplier() {
        const form = document.getElementById('supplier-form');
        const data = DB.getData();
        
        const supplierData = {
            name: document.getElementById('supplier-name').value.trim(),
            phone: document.getElementById('supplier-phone').value.trim(),
            email: document.getElementById('supplier-email').value.trim(),
            address: document.getElementById('supplier-address').value.trim(),
            contactPerson: document.getElementById('supplier-contact-person').value.trim(),
            balance: parseFloat(document.getElementById('supplier-balance').value) || 0
        };

        if (!supplierData.name) {
            alert('يرجى إدخال اسم المورد');
            return;
        }

        if (form.dataset.supplierId) {
            // تعديل مورد موجود
            const supplierId = parseInt(form.dataset.supplierId);
            const supplierIndex = data.suppliers.findIndex(s => s.id === supplierId);
            
            if (supplierIndex !== -1) {
                data.suppliers[supplierIndex] = {
                    ...data.suppliers[supplierIndex],
                    ...supplierData,
                    updatedAt: new Date().toISOString()
                };
            }
        } else {
            // إضافة مورد جديد
            const newSupplier = {
                id: DB.getNextId('supplier'),
                ...supplierData,
                createdAt: new Date().toISOString()
            };
            data.suppliers.push(newSupplier);
        }

        DB.saveData(data);
        this.hideSupplierModal();
        this.loadSuppliers();
        
        const message = form.dataset.supplierId ? 'تم تعديل المورد بنجاح' : 'تم إضافة المورد بنجاح';
        this.showNotification(message, 'success');
    },

    // تعديل المورد
    editSupplier(id) {
        const data = DB.getData();
        const supplier = data.suppliers.find(s => s.id === id);
        if (supplier) {
            this.showSupplierModal(supplier);
        }
    },

    // حذف المورد
    deleteSupplier(id) {
        if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
            const data = DB.getData();
            data.suppliers = data.suppliers.filter(s => s.id !== id);
            DB.saveData(data);
            this.loadSuppliers();
            this.showNotification('تم حذف المورد بنجاح', 'success');
        }
    },

    // عرض تفاصيل المورد
    viewSupplier(id) {
        const data = DB.getData();
        const supplier = data.suppliers.find(s => s.id === id);
        if (!supplier) return;

        // جلب معاملات المورد
        const purchases = data.purchases.filter(p => p.supplierId === id);

        const content = document.getElementById('supplier-details-content');
        content.innerHTML = `
            <div class="supplier-info">
                <h4>معلومات المورد</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <label>الاسم:</label>
                        <span>${supplier.name}</span>
                    </div>
                    <div class="info-item">
                        <label>الهاتف:</label>
                        <span>${supplier.phone || '-'}</span>
                    </div>
                    <div class="info-item">
                        <label>البريد:</label>
                        <span>${supplier.email || '-'}</span>
                    </div>
                    <div class="info-item">
                        <label>العنوان:</label>
                        <span>${supplier.address || '-'}</span>
                    </div>
                    <div class="info-item">
                        <label>الشخص المسؤول:</label>
                        <span>${supplier.contactPerson || '-'}</span>
                    </div>
                    <div class="info-item">
                        <label>الرصيد الحالي:</label>
                        <span class="balance ${supplier.balance > 0 ? 'negative' : supplier.balance < 0 ? 'positive' : ''}">${this.formatCurrency(supplier.balance)}</span>
                    </div>
                </div>
            </div>

            <div class="supplier-transactions">
                <h4>آخر المعاملات</h4>
                <div class="transactions-list">
                    ${purchases.slice(-5).map(purchase => `
                        <div class="transaction-item">
                            <span>فاتورة مشتريات #${purchase.id}</span>
                            <span>${new Date(purchase.date).toLocaleDateString('ar-SA')}</span>
                            <span class="amount">${this.formatCurrency(purchase.total)}</span>
                        </div>
                    `).join('')}
                </div>
            </div>

            <div class="supplier-actions">
                <button onclick="Purchases.newPurchaseForSupplier(${id})" class="btn btn-primary">
                    <i class="fas fa-shopping-bag"></i> فاتورة مشتريات جديدة
                </button>
                <button onclick="Reports.supplierStatement(${id})" class="btn btn-info">
                    <i class="fas fa-file-alt"></i> كشف حساب
                </button>
            </div>
        `;

        document.getElementById('supplier-details-modal').classList.remove('hidden');
    },

    // إخفاء نموذج التفاصيل
    hideDetailsModal() {
        document.getElementById('supplier-details-modal').classList.add('hidden');
    },

    // تنسيق العملة
    formatCurrency(amount) {
        const data = DB.getData();
        const currency = data.settings.currency || 'ريال';
        return `${amount.toFixed(2)} ${currency}`;
    },

    // عرض نموذج المورد المفصل
    showDetailedSupplierModal() {
        const modal = document.getElementById('detailed-supplier-modal');
        this.clearDetailedSupplierForm();
        modal.classList.remove('hidden');
    },

    // حفظ المورد المفصل
    saveDetailedSupplier() {
        const data = DB.getData();

        // جمع البيانات من النموذج
        const supplierData = {
            id: DB.getNextId('supplier'),
            code: document.getElementById('detailed-supplier-code').value.trim(),
            name: document.getElementById('detailed-supplier-name').value.trim(),
            nameEn: document.getElementById('detailed-supplier-name-en').value.trim(),
            type: document.getElementById('detailed-supplier-type').value,
            category: document.getElementById('detailed-supplier-category').value,
            status: document.getElementById('detailed-supplier-status').value,
            phone: document.getElementById('detailed-supplier-phone').value.trim(),
            phone2: document.getElementById('detailed-supplier-phone2').value.trim(),
            mobile: document.getElementById('detailed-supplier-mobile').value.trim(),
            email: document.getElementById('detailed-supplier-email').value.trim(),
            website: document.getElementById('detailed-supplier-website').value.trim(),
            fax: document.getElementById('detailed-supplier-fax').value.trim(),
            contactName: document.getElementById('detailed-contact-name').value.trim(),
            contactPosition: document.getElementById('detailed-contact-position').value.trim(),
            contactPhone: document.getElementById('detailed-contact-phone').value.trim(),
            contactEmail: document.getElementById('detailed-contact-email').value.trim(),
            contactDepartment: document.getElementById('detailed-contact-department').value.trim(),
            contactNotes: document.getElementById('detailed-contact-notes').value.trim(),
            country: document.getElementById('detailed-supplier-country').value.trim(),
            city: document.getElementById('detailed-supplier-city').value.trim(),
            district: document.getElementById('detailed-supplier-district').value.trim(),
            street: document.getElementById('detailed-supplier-street').value.trim(),
            building: document.getElementById('detailed-supplier-building').value.trim(),
            postal: document.getElementById('detailed-supplier-postal').value.trim(),
            address: document.getElementById('detailed-supplier-address').value.trim(),
            balance: parseFloat(document.getElementById('detailed-supplier-balance').value) || 0,
            creditLimit: parseFloat(document.getElementById('detailed-supplier-credit-limit').value) || 0,
            paymentTerms: parseInt(document.getElementById('detailed-supplier-payment-terms').value) || 30,
            discount: parseFloat(document.getElementById('detailed-supplier-discount').value) || 0,
            taxNumber: document.getElementById('detailed-supplier-tax-number').value.trim(),
            currency: document.getElementById('detailed-supplier-currency').value,
            established: document.getElementById('detailed-supplier-established').value,
            registration: document.getElementById('detailed-supplier-registration').value.trim(),
            rating: document.getElementById('detailed-supplier-rating').value,
            notes: document.getElementById('detailed-supplier-notes').value.trim(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // التحقق من البيانات المطلوبة
        if (!supplierData.name) {
            alert('يرجى إدخال اسم المورد');
            document.getElementById('detailed-supplier-name').focus();
            return;
        }

        // التحقق من عدم تكرار الكود إذا تم إدخاله
        if (supplierData.code) {
            const existingSupplier = data.suppliers.find(s => s.code === supplierData.code);
            if (existingSupplier) {
                alert('كود المورد موجود مسبقاً، يرجى استخدام كود آخر');
                document.getElementById('detailed-supplier-code').focus();
                return;
            }
        }

        // إضافة المورد
        data.suppliers.push(supplierData);
        DB.saveData(data);

        alert('تم حفظ المورد بنجاح');
        this.loadSuppliers();
        this.closeDetailedSupplierModal();
    },

    // حفظ وإضافة مورد آخر
    saveAndAddAnotherSupplier() {
        this.saveDetailedSupplier();
        setTimeout(() => {
            this.clearDetailedSupplierForm();
            document.getElementById('detailed-supplier-name').focus();
        }, 100);
    },

    // مسح النموذج المفصل
    clearDetailedSupplierForm() {
        const form = document.getElementById('detailed-supplier-form');
        form.reset();

        // تعيين القيم الافتراضية
        document.getElementById('detailed-supplier-type').value = 'company';
        document.getElementById('detailed-supplier-category').value = 'local';
        document.getElementById('detailed-supplier-status').value = 'active';
        document.getElementById('detailed-supplier-country').value = 'المملكة العربية السعودية';
        document.getElementById('detailed-supplier-balance').value = 0;
        document.getElementById('detailed-supplier-credit-limit').value = 0;
        document.getElementById('detailed-supplier-payment-terms').value = 30;
        document.getElementById('detailed-supplier-discount').value = 0;
        document.getElementById('detailed-supplier-currency').value = 'SAR';
    },

    // توليد كود المورد تلقائياً
    generateSupplierCode() {
        const data = DB.getData();
        const nextId = data.lastIds.supplier + 1;
        const code = `SUP${nextId.toString().padStart(6, '0')}`;
        document.getElementById('detailed-supplier-code').value = code;
    },

    // إغلاق نموذج المورد المفصل
    closeDetailedSupplierModal() {
        document.getElementById('detailed-supplier-modal').classList.add('hidden');
    },

    // عرض الإشعارات
    showNotification(message, type = 'info') {
        alert(message);
    }
};
