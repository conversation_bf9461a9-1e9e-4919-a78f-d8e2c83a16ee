/**
 * إدارة الموردين
 */
const Suppliers = {
    // عرض صفحة الموردين
    show() {
        const content = document.getElementById('page-content');
        content.innerHTML = `
            <div class="page-header">
                <h2><i class="fas fa-truck"></i> إدارة الموردين</h2>
                <button id="add-supplier-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة مورد جديد
                </button>
            </div>

            <div class="card">
                <div class="search-bar">
                    <input type="text" id="supplier-search" class="form-control" placeholder="البحث عن مورد...">
                </div>
                
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>اسم المورد</th>
                                <th>الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>الرصيد</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="suppliers-table-body">
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- نموذج إضافة/تعديل المورد -->
            <div id="supplier-modal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="modal-title">إضافة مورد جديد</h3>
                        <button id="close-modal" class="btn-close">&times;</button>
                    </div>
                    <form id="supplier-form">
                        <div class="form-group">
                            <label for="supplier-name">اسم المورد *</label>
                            <input type="text" id="supplier-name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="supplier-phone">رقم الهاتف</label>
                            <input type="tel" id="supplier-phone" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="supplier-email">البريد الإلكتروني</label>
                            <input type="email" id="supplier-email" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="supplier-address">العنوان</label>
                            <textarea id="supplier-address" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="supplier-contact-person">الشخص المسؤول</label>
                            <input type="text" id="supplier-contact-person" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="supplier-balance">الرصيد الابتدائي</label>
                            <input type="number" id="supplier-balance" class="form-control" value="0" step="0.01">
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">حفظ</button>
                            <button type="button" id="cancel-btn" class="btn btn-secondary">إلغاء</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- نموذج عرض تفاصيل المورد -->
            <div id="supplier-details-modal" class="modal hidden">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3>تفاصيل المورد</h3>
                        <button id="close-details-modal" class="btn-close">&times;</button>
                    </div>
                    <div id="supplier-details-content"></div>
                </div>
            </div>
        `;

        this.attachEvents();
        this.loadSuppliers();
    },

    // إضافة الأحداث
    attachEvents() {
        // زر إضافة مورد جديد
        document.getElementById('add-supplier-btn').addEventListener('click', () => {
            this.showSupplierModal();
        });

        // البحث
        document.getElementById('supplier-search').addEventListener('input', (e) => {
            this.searchSuppliers(e.target.value);
        });

        // نموذج المورد
        document.getElementById('supplier-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveSupplier();
        });

        // استخدام event delegation لجميع أزرار الإغلاق
        document.addEventListener('click', (e) => {
            // إغلاق نموذج المورد
            if (e.target.id === 'close-modal' || e.target.id === 'cancel-btn') {
                e.preventDefault();
                this.hideSupplierModal();
            }

            // إغلاق نموذج التفاصيل
            if (e.target.id === 'close-details-modal') {
                e.preventDefault();
                this.hideDetailsModal();
            }

            // إغلاق النوافذ عند النقر خارجها
            if (e.target.classList.contains('modal') && !e.target.classList.contains('hidden')) {
                if (e.target.id === 'supplier-modal') {
                    this.hideSupplierModal();
                } else if (e.target.id === 'supplier-details-modal') {
                    this.hideDetailsModal();
                }
            }
        });
    },

    // تحميل الموردين
    loadSuppliers() {
        const data = DB.getData();
        const suppliers = data.suppliers || [];
        this.renderSuppliers(suppliers);
    },

    // عرض الموردين في الجدول
    renderSuppliers(suppliers) {
        const tbody = document.getElementById('suppliers-table-body');
        tbody.innerHTML = '';

        if (suppliers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center">لا توجد موردين مسجلين</td>
                </tr>
            `;
            return;
        }

        suppliers.forEach(supplier => {
            const row = document.createElement('tr');
            const balanceClass = supplier.balance > 0 ? 'negative' : supplier.balance < 0 ? 'positive' : '';
            
            row.innerHTML = `
                <td>${supplier.name}</td>
                <td>${supplier.phone || '-'}</td>
                <td>${supplier.email || '-'}</td>
                <td class="balance ${balanceClass}">${this.formatCurrency(supplier.balance)}</td>
                <td class="actions">
                    <button onclick="Suppliers.viewSupplier(${supplier.id})" class="btn btn-sm btn-info">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button onclick="Suppliers.editSupplier(${supplier.id})" class="btn btn-sm btn-warning">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="Suppliers.deleteSupplier(${supplier.id})" class="btn btn-sm btn-danger">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    },

    // البحث عن الموردين
    searchSuppliers(query) {
        const suppliers = DB.search('suppliers', query, ['name', 'phone', 'email', 'contactPerson']);
        this.renderSuppliers(suppliers);
    },

    // عرض نموذج المورد
    showSupplierModal(supplier = null) {
        const modal = document.getElementById('supplier-modal');
        const title = document.getElementById('modal-title');
        const form = document.getElementById('supplier-form');

        if (supplier) {
            title.textContent = 'تعديل المورد';
            document.getElementById('supplier-name').value = supplier.name;
            document.getElementById('supplier-phone').value = supplier.phone || '';
            document.getElementById('supplier-email').value = supplier.email || '';
            document.getElementById('supplier-address').value = supplier.address || '';
            document.getElementById('supplier-contact-person').value = supplier.contactPerson || '';
            document.getElementById('supplier-balance').value = supplier.balance || 0;
            form.dataset.supplierId = supplier.id;
        } else {
            title.textContent = 'إضافة مورد جديد';
            form.reset();
            delete form.dataset.supplierId;
        }

        modal.classList.remove('hidden');
    },

    // إخفاء نموذج المورد
    hideSupplierModal() {
        document.getElementById('supplier-modal').classList.add('hidden');
    },

    // حفظ المورد
    saveSupplier() {
        const form = document.getElementById('supplier-form');
        const data = DB.getData();
        
        const supplierData = {
            name: document.getElementById('supplier-name').value.trim(),
            phone: document.getElementById('supplier-phone').value.trim(),
            email: document.getElementById('supplier-email').value.trim(),
            address: document.getElementById('supplier-address').value.trim(),
            contactPerson: document.getElementById('supplier-contact-person').value.trim(),
            balance: parseFloat(document.getElementById('supplier-balance').value) || 0
        };

        if (!supplierData.name) {
            alert('يرجى إدخال اسم المورد');
            return;
        }

        if (form.dataset.supplierId) {
            // تعديل مورد موجود
            const supplierId = parseInt(form.dataset.supplierId);
            const supplierIndex = data.suppliers.findIndex(s => s.id === supplierId);
            
            if (supplierIndex !== -1) {
                data.suppliers[supplierIndex] = {
                    ...data.suppliers[supplierIndex],
                    ...supplierData,
                    updatedAt: new Date().toISOString()
                };
            }
        } else {
            // إضافة مورد جديد
            const newSupplier = {
                id: DB.getNextId('supplier'),
                ...supplierData,
                createdAt: new Date().toISOString()
            };
            data.suppliers.push(newSupplier);
        }

        DB.saveData(data);
        this.hideSupplierModal();
        this.loadSuppliers();
        
        const message = form.dataset.supplierId ? 'تم تعديل المورد بنجاح' : 'تم إضافة المورد بنجاح';
        this.showNotification(message, 'success');
    },

    // تعديل المورد
    editSupplier(id) {
        const data = DB.getData();
        const supplier = data.suppliers.find(s => s.id === id);
        if (supplier) {
            this.showSupplierModal(supplier);
        }
    },

    // حذف المورد
    deleteSupplier(id) {
        if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
            const data = DB.getData();
            data.suppliers = data.suppliers.filter(s => s.id !== id);
            DB.saveData(data);
            this.loadSuppliers();
            this.showNotification('تم حذف المورد بنجاح', 'success');
        }
    },

    // عرض تفاصيل المورد
    viewSupplier(id) {
        const data = DB.getData();
        const supplier = data.suppliers.find(s => s.id === id);
        if (!supplier) return;

        // جلب معاملات المورد
        const purchases = data.purchases.filter(p => p.supplierId === id);

        const content = document.getElementById('supplier-details-content');
        content.innerHTML = `
            <div class="supplier-info">
                <h4>معلومات المورد</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <label>الاسم:</label>
                        <span>${supplier.name}</span>
                    </div>
                    <div class="info-item">
                        <label>الهاتف:</label>
                        <span>${supplier.phone || '-'}</span>
                    </div>
                    <div class="info-item">
                        <label>البريد:</label>
                        <span>${supplier.email || '-'}</span>
                    </div>
                    <div class="info-item">
                        <label>العنوان:</label>
                        <span>${supplier.address || '-'}</span>
                    </div>
                    <div class="info-item">
                        <label>الشخص المسؤول:</label>
                        <span>${supplier.contactPerson || '-'}</span>
                    </div>
                    <div class="info-item">
                        <label>الرصيد الحالي:</label>
                        <span class="balance ${supplier.balance > 0 ? 'negative' : supplier.balance < 0 ? 'positive' : ''}">${this.formatCurrency(supplier.balance)}</span>
                    </div>
                </div>
            </div>

            <div class="supplier-transactions">
                <h4>آخر المعاملات</h4>
                <div class="transactions-list">
                    ${purchases.slice(-5).map(purchase => `
                        <div class="transaction-item">
                            <span>فاتورة مشتريات #${purchase.id}</span>
                            <span>${new Date(purchase.date).toLocaleDateString('ar-SA')}</span>
                            <span class="amount">${this.formatCurrency(purchase.total)}</span>
                        </div>
                    `).join('')}
                </div>
            </div>

            <div class="supplier-actions">
                <button onclick="Purchases.newPurchaseForSupplier(${id})" class="btn btn-primary">
                    <i class="fas fa-shopping-bag"></i> فاتورة مشتريات جديدة
                </button>
                <button onclick="Reports.supplierStatement(${id})" class="btn btn-info">
                    <i class="fas fa-file-alt"></i> كشف حساب
                </button>
            </div>
        `;

        document.getElementById('supplier-details-modal').classList.remove('hidden');
    },

    // إخفاء نموذج التفاصيل
    hideDetailsModal() {
        document.getElementById('supplier-details-modal').classList.add('hidden');
    },

    // تنسيق العملة
    formatCurrency(amount) {
        const data = DB.getData();
        const currency = data.settings.currency || 'ريال';
        return `${amount.toFixed(2)} ${currency}`;
    },

    // عرض الإشعارات
    showNotification(message, type = 'info') {
        alert(message);
    }
};
