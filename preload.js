const { app, BrowserWindow, Menu, ipc<PERSON>ain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');

// حفظ مرجع للنافذة الرئيسية لمنع إغلاقها تلقائيًا بواسطة جامع النفايات
let mainWindow;

// إنشاء النافذة الرئيسية
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true,
      nodeIntegration: false
    },
    icon: path.join(__dirname, 'icon.ico')
  });

  // تحميل الصفحة الرئيسية
  mainWindow.loadFile('index.html');

  // إنشاء قائمة التطبيق
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'تصدير البيانات',
          click: () => {
            mainWindow.webContents.send('export-data');
          }
        },
        {
          label: 'استيراد البيانات',
          click: () => {
            mainWindow.webContents.send('import-data');
          }
        },
        { type: 'separator' },
        {
          label: 'خروج',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        {
          label: 'تحديث',
          click: () => {
            mainWindow.reload();
          }
        },
        {
          label: 'أدوات المطور',
          click: () => {
            mainWindow.webContents.toggleDevTools();
          }
        },
        { type: 'separator' },
        {
          label: 'ملء الشاشة',
          click: () => {
            mainWindow.setFullScreen(!mainWindow.isFullScreen());
          }
        }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'حول البرنامج',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'حول البرنامج',
              message: 'نظام محاسبين ولكن لإدارة المبيعات',
              detail: 'الإصدار 1.0.0\nجميع الحقوق محفوظة © 2023',
              buttons: ['موافق']
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);

  // عند إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// عند جاهزية التطبيق
app.whenReady().then(() => {
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// عند إغلاق جميع النوافذ
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// استقبال طلبات من الواجهة
ipcMain.handle('save-file', async (event, data) => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      title: 'حفظ الملف',
      defaultPath: path.join(app.getPath('documents'), 'pos-data.json'),
      filters: [
        { name: 'ملفات JSON', extensions: ['json'] }
      ]
    });
    
    if (!result.canceled && result.filePath) {
      fs.writeFileSync(result.filePath, data);
      return { success: true, path: result.filePath };
    } else {
      return { success: false };
    }
  } catch (err) {
    return { success: false, error: err.message };
  }
});

ipcMain.handle('open-file', async () => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, {
      title: 'فتح ملف',
      defaultPath: app.getPath('documents'),
      filters: [
        { name: 'ملفات JSON', extensions: ['json'] }
      ],
      properties: ['openFile']
    });
    
    if (!result.canceled && result.filePaths.length > 0) {
      const filePath = result.filePaths[0];
      const fileData = fs.readFileSync(filePath, 'utf8');
      return { success: true, data: fileData };
    } else {
      return { success: false };
    }
  } catch (err) {
    return { success: false, error: err.message };
  }
});

const { contextBridge, ipcRenderer } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  saveFile: (data) => {
    return ipcRenderer.invoke('save-file', data);
  },
  
  openFile: () => {
    return ipcRenderer.invoke('open-file');
  },
  
  onExportData: (callback) => {
    ipcRenderer.on('export-data', () => callback());
    return () => ipcRenderer.removeAllListeners('export-data');
  },
  
  onImportData: (callback) => {
    ipcRenderer.on('import-data', () => callback());
    return () => ipcRenderer.removeAllListeners('import-data');
  }
});

