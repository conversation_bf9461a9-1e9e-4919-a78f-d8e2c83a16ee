<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وحدات التطبيق</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #ddd;
        }
        .test-success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .test-error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .test-warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .module-name {
            font-weight: bold;
            font-size: 16px;
        }
        .test-details {
            font-size: 14px;
            margin-top: 5px;
        }
        .summary {
            background: #e9ecef;
            padding: 20px;
            border-radius: 5px;
            margin-top: 30px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار وحدات نظام نقاط البيع</h1>
        <div id="test-results"></div>
        <div id="summary" class="summary"></div>
    </div>

    <!-- تحميل جميع الوحدات -->
    <script src="js/core.js"></script>
    <script src="js/products.js"></script>
    <script src="js/customers.js"></script>
    <script src="js/suppliers.js"></script>
    <script src="js/sales.js"></script>
    <script src="js/purchases.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/settings.js"></script>

    <script>
        // اختبار الوحدات
        function runTests() {
            const results = [];
            const testResults = document.getElementById('test-results');
            
            // قائمة الوحدات المطلوب اختبارها
            const modules = [
                { name: 'DB', displayName: 'قاعدة البيانات', required: ['getData', 'saveData', 'getNextId', 'search'] },
                { name: 'Auth', displayName: 'المصادقة', required: ['login', 'logout', 'isLoggedIn'] },
                { name: 'App', displayName: 'التطبيق الرئيسي', required: ['init', 'showPage'] },
                { name: 'Products', displayName: 'المنتجات', required: ['show', 'loadProducts'] },
                { name: 'Customers', displayName: 'العملاء', required: ['show', 'loadCustomers'] },
                { name: 'Suppliers', displayName: 'الموردين', required: ['show', 'loadSuppliers'] },
                { name: 'Sales', displayName: 'المبيعات', required: ['show', 'loadSales'] },
                { name: 'Purchases', displayName: 'المشتريات', required: ['show', 'loadPurchases'] },
                { name: 'Reports', displayName: 'التقارير', required: ['show', 'loadQuickStats'] },
                { name: 'Settings', displayName: 'الإعدادات', required: ['show'] }
            ];

            let successCount = 0;
            let errorCount = 0;
            let warningCount = 0;

            modules.forEach(module => {
                const testItem = document.createElement('div');
                testItem.className = 'test-item';
                
                try {
                    // التحقق من وجود الوحدة
                    if (typeof window[module.name] === 'undefined') {
                        throw new Error(`الوحدة ${module.name} غير محملة`);
                    }

                    const moduleObj = window[module.name];
                    const missingMethods = [];

                    // التحقق من وجود الوظائف المطلوبة
                    module.required.forEach(method => {
                        if (typeof moduleObj[method] !== 'function') {
                            missingMethods.push(method);
                        }
                    });

                    if (missingMethods.length > 0) {
                        testItem.className += ' test-warning';
                        testItem.innerHTML = `
                            <div class="module-name">⚠️ ${module.displayName}</div>
                            <div class="test-details">الوحدة محملة ولكن تفتقد للوظائف: ${missingMethods.join(', ')}</div>
                        `;
                        warningCount++;
                    } else {
                        testItem.className += ' test-success';
                        testItem.innerHTML = `
                            <div class="module-name">✅ ${module.displayName}</div>
                            <div class="test-details">جميع الوظائف متوفرة ومحملة بنجاح</div>
                        `;
                        successCount++;
                    }

                } catch (error) {
                    testItem.className += ' test-error';
                    testItem.innerHTML = `
                        <div class="module-name">❌ ${module.displayName}</div>
                        <div class="test-details">خطأ: ${error.message}</div>
                    `;
                    errorCount++;
                }

                testResults.appendChild(testItem);
            });

            // عرض الملخص
            const summary = document.getElementById('summary');
            const total = modules.length;
            const successRate = Math.round((successCount / total) * 100);
            
            summary.innerHTML = `
                <h3>📊 ملخص الاختبار</h3>
                <p><strong>إجمالي الوحدات:</strong> ${total}</p>
                <p><strong>نجح:</strong> ${successCount} | <strong>تحذيرات:</strong> ${warningCount} | <strong>أخطاء:</strong> ${errorCount}</p>
                <p><strong>معدل النجاح:</strong> ${successRate}%</p>
                ${successRate >= 80 ? 
                    '<p style="color: #28a745; font-weight: bold;">🎉 التطبيق جاهز للاستخدام!</p>' : 
                    '<p style="color: #dc3545; font-weight: bold;">⚠️ يحتاج التطبيق لإصلاحات قبل الاستخدام</p>'
                }
            `;

            // اختبار إضافي لقاعدة البيانات
            testDatabase();
        }

        // اختبار قاعدة البيانات
        function testDatabase() {
            const testItem = document.createElement('div');
            testItem.className = 'test-item';
            
            try {
                // اختبار قاعدة البيانات
                const data = DB.getData();
                const hasRequiredTables = ['products', 'customers', 'suppliers', 'sales', 'purchases', 'categories', 'settings'].every(table => 
                    Array.isArray(data[table])
                );

                if (hasRequiredTables) {
                    testItem.className += ' test-success';
                    testItem.innerHTML = `
                        <div class="module-name">✅ هيكل قاعدة البيانات</div>
                        <div class="test-details">جميع الجداول المطلوبة موجودة ومُهيأة بشكل صحيح</div>
                    `;
                } else {
                    testItem.className += ' test-warning';
                    testItem.innerHTML = `
                        <div class="module-name">⚠️ هيكل قاعدة البيانات</div>
                        <div class="test-details">بعض الجداول مفقودة أو غير مُهيأة بشكل صحيح</div>
                    `;
                }
            } catch (error) {
                testItem.className += ' test-error';
                testItem.innerHTML = `
                    <div class="module-name">❌ هيكل قاعدة البيانات</div>
                    <div class="test-details">خطأ في الوصول لقاعدة البيانات: ${error.message}</div>
                `;
            }

            document.getElementById('test-results').appendChild(testItem);
        }

        // تشغيل الاختبارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // انتظار أطول للتأكد من تحميل جميع الوحدات
            setTimeout(() => {
                console.log('بدء الاختبارات...');
                console.log('الوحدات المتاحة:', Object.keys(window).filter(key =>
                    ['DB', 'Auth', 'App', 'Products', 'Customers', 'Suppliers', 'Sales', 'Purchases', 'Reports', 'Settings'].includes(key)
                ));
                runTests();
            }, 500);
        });
    </script>
</body>
</html>
