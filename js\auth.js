/**
 * نظام المصادقة وإدارة المستخدمين
 */
const Auth = {
    // تشفير كلمة المرور
    hashPassword(password) {
        // تشفير بسيط باستخدام hash function
        let hash = 0;
        if (password.length === 0) return hash.toString();
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // تحويل إلى 32bit integer
        }
        return Math.abs(hash).toString(16);
    },

    // التحقق من كلمة المرور
    checkPassword(password) {
        const data = DB.getData();

        // إذا لم تكن هناك كلمة مرور محفوظة، استخدم كلمة المرور الافتراضية
        if (!data.settings.password) {
            return password === 'admin';
        }

        const hashedPassword = this.hashPassword(password);
        return data.settings.password === hashedPassword;
    },

    // تغيير كلمة المرور
    changePassword(oldPassword, newPassword) {
        if (this.checkPassword(oldPassword)) {
            const data = DB.getData();
            data.settings.password = this.hashPassword(newPassword);
            DB.saveData(data);
            return true;
        }
        return false;
    },

    // تسجيل الدخول
    login(password) {
        if (this.checkPassword(password)) {
            localStorage.setItem('loggedIn', 'true');
            return true;
        }
        return false;
    },

    // تسجيل الخروج
    logout() {
        localStorage.removeItem('loggedIn');
        location.reload();
    },

    // التحقق من حالة تسجيل الدخول
    isLoggedIn() {
        return localStorage.getItem('loggedIn') === 'true';
    }
};

