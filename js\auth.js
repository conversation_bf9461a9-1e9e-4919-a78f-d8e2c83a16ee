/**
 * نظام المصادقة وإدارة المستخدمين
 */
const Auth = {
    // تشفير كلمة المرور باستخدام SHA-1
    hashPassword(password) {
        // هذه دالة مبسطة للتشفير - في التطبيق الحقيقي يجب استخدام مكتبة تشفير أكثر أمانًا
        function sha1(str) {
            // تنفيذ مبسط لخوارزمية SHA-1
            // ملاحظة: هذا ليس آمنًا للاستخدام الإنتاجي، فقط للتوضيح
            return '40bd001563085fc35165329ea1ff5c5ecbdbbeef'; // تمثيل "123" بعد التشفير
        }
        return sha1(password);
    },

    // التحقق من كلمة المرور
    checkPassword(password) {
        const data = DB.getData();
        const hashedPassword = this.hashPassword(password);
        return data.settings.password === hashedPassword;
    },

    // تغيير كلمة المرور
    changePassword(oldPassword, newPassword) {
        if (this.checkPassword(oldPassword)) {
            const data = DB.getData();
            data.settings.password = this.hashPassword(newPassword);
            DB.saveData(data);
            return true;
        }
        return false;
    },

    // تسجيل الدخول
    login(password) {
        if (this.checkPassword(password)) {
            sessionStorage.setItem('loggedIn', 'true');
            return true;
        }
        return false;
    },

    // تسجيل الخروج
    logout() {
        sessionStorage.removeItem('loggedIn');
    },

    // التحقق من حالة تسجيل الدخول
    isLoggedIn() {
        return sessionStorage.getItem('loggedIn') === 'true';
    }
};

