<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محاسبين ولكن - نظام إدارة البيع</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div id="app">
        <div id="login-container" class="container"></div>
        <div id="main-container" class="container hidden"></div>
    </div>

    <!-- ملفات جافاسكريبت -->
    <script src="js/core.js"></script>
    <script src="js/products.js"></script>
    <script src="js/customers.js"></script>
    <script src="js/suppliers.js"></script>
    <script src="js/sales.js"></script>
    <script src="js/purchases.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/settings.js"></script>
    <script>
        // تهيئة التطبيق عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing app...');

            try {
                // التحقق من وجود الوحدات الأساسية
                if (typeof DB === 'undefined') {
                    throw new Error('وحدة قاعدة البيانات غير محملة');
                }

                if (typeof Auth === 'undefined') {
                    throw new Error('وحدة المصادقة غير محملة');
                }

                if (typeof App === 'undefined') {
                    throw new Error('وحدة التطبيق الرئيسية غير محملة');
                }

                console.log('Initializing app...');
                App.init();

                console.log('App initialized successfully');
            } catch (error) {
                console.error('Error initializing app:', error);
                document.body.innerHTML = `
                    <div style="padding: 20px; text-align: center; font-family: 'Tajawal', Arial, sans-serif; direction: rtl;">
                        <h2 style="color: #e74c3c;">خطأ في تحميل التطبيق</h2>
                        <p style="color: #333; margin: 20px 0;">${error.message}</p>
                        <button onclick="location.reload()" style="padding: 10px 20px; font-size: 16px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer;">إعادة تحميل</button>
                    </div>
                `;
            }
        });

        // معالجة الأخطاء العامة
        window.addEventListener('error', function(event) {
            console.error('Global error:', event.error);
        });

        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled promise rejection:', event.reason);
        });
    </script>
</body>
</html>

