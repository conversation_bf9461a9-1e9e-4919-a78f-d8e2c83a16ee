/**
 * إدارة قاعدة البيانات المحلية باستخدام localStorage
 */
const DB = {
    // مفتاح التخزين الرئيسي
    storageKey: 'accountants_pos_data',

    // هيكل البيانات الافتراضي
    defaultData: {
        settings: {
            password: '40bd001563085fc35165329ea1ff5c5ecbdbbeef', // كلمة المرور الافتراضية "123"
            theme: 'light',
            companyInfo: {
                name: 'شركة محاسبين ولكن',
                address: 'العنوان',
                phone: '**********',
                email: '<EMAIL>',
                logo: ''
            },
            taxRate: 15, // نسبة الضريبة بالمئة
            currency: 'ريال سعودي',
            printSettings: {
                showLogo: true,
                showCompanyInfo: true,
                paperSize: 'A4'
            }
        },
        products: [],
        customers: [
            {
                id: 1,
                name: 'ضيف',
                phone: '',
                email: '',
                address: '',
                balance: 0,
                isDefault: true,
                createdAt: new Date().toISOString()
            }
        ],
        suppliers: [],
        sales: [],
        purchases: [],
        categories: [
            { id: 1, name: 'عام', description: 'فئة عامة' }
        ],
        accounts: [
            // دليل الحسابات
            { id: 1, code: '1000', name: 'الأصول', type: 'asset', parentId: null },
            { id: 2, code: '1100', name: 'الأصول المتداولة', type: 'asset', parentId: 1 },
            { id: 3, code: '1110', name: 'النقدية', type: 'asset', parentId: 2 },
            { id: 4, code: '1120', name: 'المخزون', type: 'asset', parentId: 2 },
            { id: 5, code: '1130', name: 'العملاء', type: 'asset', parentId: 2 },

            { id: 6, code: '2000', name: 'الخصوم', type: 'liability', parentId: null },
            { id: 7, code: '2100', name: 'الخصوم المتداولة', type: 'liability', parentId: 6 },
            { id: 8, code: '2110', name: 'الموردين', type: 'liability', parentId: 7 },
            { id: 9, code: '2120', name: 'الضرائب المستحقة', type: 'liability', parentId: 7 },

            { id: 10, code: '3000', name: 'حقوق الملكية', type: 'equity', parentId: null },
            { id: 11, code: '3100', name: 'رأس المال', type: 'equity', parentId: 10 },
            { id: 12, code: '3200', name: 'الأرباح المحتجزة', type: 'equity', parentId: 10 },

            { id: 13, code: '4000', name: 'الإيرادات', type: 'revenue', parentId: null },
            { id: 14, code: '4100', name: 'إيرادات المبيعات', type: 'revenue', parentId: 13 },

            { id: 15, code: '5000', name: 'المصروفات', type: 'expense', parentId: null },
            { id: 16, code: '5100', name: 'تكلفة البضاعة المباعة', type: 'expense', parentId: 15 },
            { id: 17, code: '5200', name: 'مصروفات التشغيل', type: 'expense', parentId: 15 }
        ],
        transactions: [], // المعاملات المحاسبية
        journals: [], // اليوميات المحاسبية
        dailyExpenses: [], // المصروفات اليومية
        payments: [], // المدفوعات
        lastIds: {
            product: 0,
            customer: 1,
            supplier: 0,
            sale: 0,
            purchase: 0,
            category: 1,
            account: 17,
            transaction: 0,
            journal: 0,
            expense: 0,
            payment: 0
        }
    },

    // تهيئة قاعدة البيانات
    init() {
        const data = this.getData();
        if (!data || Object.keys(data).length === 0) {
            this.saveData(this.defaultData);
        }

        // التأكد من وجود جميع الحقول المطلوبة
        const currentData = this.getData();
        let updated = false;

        // إضافة الحقول المفقودة
        Object.keys(this.defaultData).forEach(key => {
            if (!currentData.hasOwnProperty(key)) {
                currentData[key] = this.defaultData[key];
                updated = true;
            }
        });

        if (updated) {
            this.saveData(currentData);
        }
    },

    // جلب البيانات من localStorage
    getData() {
        try {
            const data = localStorage.getItem(this.storageKey);
            return data ? JSON.parse(data) : this.defaultData;
        } catch (error) {
            console.error('خطأ في جلب البيانات:', error);
            return this.defaultData;
        }
    },

    // حفظ البيانات في localStorage
    saveData(data) {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            return false;
        }
    },

    // إنشاء نسخة احتياطية
    backup() {
        const data = this.getData();
        const backup = {
            data: data,
            timestamp: new Date().toISOString(),
            version: '1.0.0'
        };

        const blob = new Blob([JSON.stringify(backup, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `backup_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    },

    // استعادة من نسخة احتياطية
    restore(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const backup = JSON.parse(e.target.result);
                    if (backup.data) {
                        this.saveData(backup.data);
                        resolve(true);
                    } else {
                        reject('ملف النسخة الاحتياطية غير صالح');
                    }
                } catch (error) {
                    reject('خطأ في قراءة ملف النسخة الاحتياطية');
                }
            };
            reader.readAsText(file);
        });
    },

    // مسح جميع البيانات
    clearAll() {
        localStorage.removeItem(this.storageKey);
        this.init();
    },

    // الحصول على معرف جديد
    getNextId(type) {
        const data = this.getData();
        data.lastIds[type]++;
        this.saveData(data);
        return data.lastIds[type];
    },

    // البحث في البيانات
    search(collection, query, fields = []) {
        const data = this.getData();
        const items = data[collection] || [];

        if (!query) return items;

        return items.filter(item => {
            if (fields.length === 0) {
                // البحث في جميع الحقول
                return Object.values(item).some(value =>
                    String(value).toLowerCase().includes(query.toLowerCase())
                );
            } else {
                // البحث في حقول محددة
                return fields.some(field =>
                    item[field] && String(item[field]).toLowerCase().includes(query.toLowerCase())
                );
            }
        });
    }
};


