/**
 * إدارة العملاء
 */
const Customers = {
    // عرض صفحة العملاء
    show() {
        const content = document.getElementById('page-content');
        content.innerHTML = `
            <div class="page-header">
                <h2><i class="fas fa-users"></i> إدارة العملاء</h2>
                <button id="add-customer-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة عميل جديد
                </button>
            </div>

            <div class="card">
                <div class="search-bar">
                    <input type="text" id="customer-search" class="form-control" placeholder="البحث عن عميل...">
                </div>
                
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>الرصيد</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="customers-table-body">
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- نموذج إضافة/تعديل العميل -->
            <div id="customer-modal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="modal-title">إضافة عميل جديد</h3>
                        <button id="close-modal" class="btn-close">&times;</button>
                    </div>
                    <form id="customer-form">
                        <div class="form-group">
                            <label for="customer-name">اسم العميل *</label>
                            <input type="text" id="customer-name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="customer-phone">رقم الهاتف</label>
                            <input type="tel" id="customer-phone" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="customer-email">البريد الإلكتروني</label>
                            <input type="email" id="customer-email" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="customer-address">العنوان</label>
                            <textarea id="customer-address" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="customer-balance">الرصيد الابتدائي</label>
                            <input type="number" id="customer-balance" class="form-control" value="0" step="0.01">
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">حفظ</button>
                            <button type="button" id="cancel-btn" class="btn btn-secondary">إلغاء</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- نموذج عرض تفاصيل العميل -->
            <div id="customer-details-modal" class="modal hidden">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3>تفاصيل العميل</h3>
                        <button id="close-details-modal" class="btn-close">&times;</button>
                    </div>
                    <div id="customer-details-content"></div>
                </div>
            </div>
        `;

        this.attachEvents();
        this.loadCustomers();
    },

    // إضافة الأحداث
    attachEvents() {
        // زر إضافة عميل جديد
        document.getElementById('add-customer-btn').addEventListener('click', () => {
            this.showCustomerModal();
        });

        // البحث
        document.getElementById('customer-search').addEventListener('input', (e) => {
            this.searchCustomers(e.target.value);
        });

        // نموذج العميل
        document.getElementById('customer-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCustomer();
        });

        // استخدام event delegation لجميع أزرار الإغلاق
        document.addEventListener('click', (e) => {
            // إغلاق نموذج العميل
            if (e.target.id === 'close-modal' || e.target.id === 'cancel-btn') {
                e.preventDefault();
                this.hideCustomerModal();
            }

            // إغلاق نموذج التفاصيل
            if (e.target.id === 'close-details-modal') {
                e.preventDefault();
                this.hideDetailsModal();
            }

            // إغلاق النوافذ عند النقر خارجها
            if (e.target.classList.contains('modal') && !e.target.classList.contains('hidden')) {
                if (e.target.id === 'customer-modal') {
                    this.hideCustomerModal();
                } else if (e.target.id === 'customer-details-modal') {
                    this.hideDetailsModal();
                }
            }
        });
    },

    // تحميل العملاء
    loadCustomers() {
        const data = DB.getData();
        const customers = data.customers || [];
        this.renderCustomers(customers);
    },

    // عرض العملاء في الجدول
    renderCustomers(customers) {
        const tbody = document.getElementById('customers-table-body');
        tbody.innerHTML = '';

        customers.forEach(customer => {
            const row = document.createElement('tr');
            const balanceClass = customer.balance > 0 ? 'positive' : customer.balance < 0 ? 'negative' : '';
            
            row.innerHTML = `
                <td>${customer.name}</td>
                <td>${customer.phone || '-'}</td>
                <td>${customer.email || '-'}</td>
                <td class="balance ${balanceClass}">${this.formatCurrency(customer.balance)}</td>
                <td class="actions">
                    <button onclick="Customers.viewCustomer(${customer.id})" class="btn btn-sm btn-info">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${!customer.isDefault ? `
                        <button onclick="Customers.editCustomer(${customer.id})" class="btn btn-sm btn-warning">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="Customers.deleteCustomer(${customer.id})" class="btn btn-sm btn-danger">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </td>
            `;
            tbody.appendChild(row);
        });
    },

    // البحث عن العملاء
    searchCustomers(query) {
        const customers = DB.search('customers', query, ['name', 'phone', 'email']);
        this.renderCustomers(customers);
    },

    // عرض نموذج العميل
    showCustomerModal(customer = null) {
        const modal = document.getElementById('customer-modal');
        const title = document.getElementById('modal-title');
        const form = document.getElementById('customer-form');

        if (customer) {
            title.textContent = 'تعديل العميل';
            document.getElementById('customer-name').value = customer.name;
            document.getElementById('customer-phone').value = customer.phone || '';
            document.getElementById('customer-email').value = customer.email || '';
            document.getElementById('customer-address').value = customer.address || '';
            document.getElementById('customer-balance').value = customer.balance || 0;
            form.dataset.customerId = customer.id;
        } else {
            title.textContent = 'إضافة عميل جديد';
            form.reset();
            delete form.dataset.customerId;
        }

        modal.classList.remove('hidden');
    },

    // إخفاء نموذج العميل
    hideCustomerModal() {
        document.getElementById('customer-modal').classList.add('hidden');
    },

    // حفظ العميل
    saveCustomer() {
        const form = document.getElementById('customer-form');
        const data = DB.getData();
        
        const customerData = {
            name: document.getElementById('customer-name').value.trim(),
            phone: document.getElementById('customer-phone').value.trim(),
            email: document.getElementById('customer-email').value.trim(),
            address: document.getElementById('customer-address').value.trim(),
            balance: parseFloat(document.getElementById('customer-balance').value) || 0
        };

        if (!customerData.name) {
            alert('يرجى إدخال اسم العميل');
            return;
        }

        if (form.dataset.customerId) {
            // تعديل عميل موجود
            const customerId = parseInt(form.dataset.customerId);
            const customerIndex = data.customers.findIndex(c => c.id === customerId);
            
            if (customerIndex !== -1) {
                data.customers[customerIndex] = {
                    ...data.customers[customerIndex],
                    ...customerData,
                    updatedAt: new Date().toISOString()
                };
            }
        } else {
            // إضافة عميل جديد
            const newCustomer = {
                id: DB.getNextId('customer'),
                ...customerData,
                isDefault: false,
                createdAt: new Date().toISOString()
            };
            data.customers.push(newCustomer);
        }

        DB.saveData(data);
        this.hideCustomerModal();
        this.loadCustomers();
        
        const message = form.dataset.customerId ? 'تم تعديل العميل بنجاح' : 'تم إضافة العميل بنجاح';
        this.showNotification(message, 'success');
    },

    // تعديل العميل
    editCustomer(id) {
        const data = DB.getData();
        const customer = data.customers.find(c => c.id === id);
        if (customer) {
            this.showCustomerModal(customer);
        }
    },

    // حذف العميل
    deleteCustomer(id) {
        if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
            const data = DB.getData();
            data.customers = data.customers.filter(c => c.id !== id);
            DB.saveData(data);
            this.loadCustomers();
            this.showNotification('تم حذف العميل بنجاح', 'success');
        }
    },

    // عرض تفاصيل العميل
    viewCustomer(id) {
        const data = DB.getData();
        const customer = data.customers.find(c => c.id === id);
        if (!customer) return;

        // جلب معاملات العميل
        const sales = data.sales.filter(s => s.customerId === id);
        const payments = data.payments.filter(p => p.customerId === id);

        const content = document.getElementById('customer-details-content');
        content.innerHTML = `
            <div class="customer-info">
                <h4>معلومات العميل</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <label>الاسم:</label>
                        <span>${customer.name}</span>
                    </div>
                    <div class="info-item">
                        <label>الهاتف:</label>
                        <span>${customer.phone || '-'}</span>
                    </div>
                    <div class="info-item">
                        <label>البريد:</label>
                        <span>${customer.email || '-'}</span>
                    </div>
                    <div class="info-item">
                        <label>العنوان:</label>
                        <span>${customer.address || '-'}</span>
                    </div>
                    <div class="info-item">
                        <label>الرصيد الحالي:</label>
                        <span class="balance ${customer.balance > 0 ? 'positive' : customer.balance < 0 ? 'negative' : ''}">${this.formatCurrency(customer.balance)}</span>
                    </div>
                </div>
            </div>

            <div class="customer-transactions">
                <h4>آخر المعاملات</h4>
                <div class="transactions-list">
                    ${sales.slice(-5).map(sale => `
                        <div class="transaction-item">
                            <span>فاتورة مبيعات #${sale.id}</span>
                            <span>${new Date(sale.date).toLocaleDateString('ar-SA')}</span>
                            <span class="amount">${this.formatCurrency(sale.total)}</span>
                        </div>
                    `).join('')}
                </div>
            </div>

            <div class="customer-actions">
                <button onclick="Sales.newSaleForCustomer(${id})" class="btn btn-primary">
                    <i class="fas fa-shopping-cart"></i> فاتورة جديدة
                </button>
                <button onclick="Customers.addPayment(${id})" class="btn btn-success">
                    <i class="fas fa-money-bill"></i> تسجيل دفعة
                </button>
                <button onclick="Reports.customerStatement(${id})" class="btn btn-info">
                    <i class="fas fa-file-alt"></i> كشف حساب
                </button>
            </div>
        `;

        document.getElementById('customer-details-modal').classList.remove('hidden');
    },

    // إخفاء نموذج التفاصيل
    hideDetailsModal() {
        document.getElementById('customer-details-modal').classList.add('hidden');
    },

    // تنسيق العملة
    formatCurrency(amount) {
        const data = DB.getData();
        const currency = data.settings.currency || 'ريال';
        return `${amount.toFixed(2)} ${currency}`;
    },

    // عرض الإشعارات
    showNotification(message, type = 'info') {
        // يمكن تطوير نظام إشعارات أكثر تقدماً لاحقاً
        alert(message);
    }
};
