/**
 * إدارة العملاء
 */
const Customers = {
    // عرض صفحة العملاء
    show() {
        const content = document.getElementById('page-content');
        content.innerHTML = `
            <div class="page-header">
                <h2><i class="fas fa-users"></i> إدارة العملاء</h2>
                <div>
                    <button id="add-customer-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة عميل سريع
                    </button>
                    <button id="add-detailed-customer-btn" class="btn btn-success">
                        <i class="fas fa-user-plus"></i> إضافة عميل بكامل البيانات
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="search-bar">
                    <input type="text" id="customer-search" class="form-control" placeholder="البحث عن عميل...">
                </div>
                
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>الرصيد</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="customers-table-body">
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- نموذج إضافة/تعديل العميل -->
            <div id="customer-modal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="modal-title">إضافة عميل جديد</h3>
                        <button id="close-modal" class="btn-close" onclick="closeModal('customer-modal')">&times;</button>
                    </div>
                    <form id="customer-form">
                        <div class="form-group">
                            <label for="customer-name">اسم العميل *</label>
                            <input type="text" id="customer-name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="customer-phone">رقم الهاتف</label>
                            <input type="tel" id="customer-phone" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="customer-email">البريد الإلكتروني</label>
                            <input type="email" id="customer-email" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="customer-address">العنوان</label>
                            <textarea id="customer-address" class="form-control" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="customer-balance">الرصيد الابتدائي</label>
                            <input type="number" id="customer-balance" class="form-control" value="0" step="0.01">
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ
                            </button>
                            <button type="button" onclick="Customers.editCustomer()" class="btn btn-warning">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button type="button" onclick="App.goBack()" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- نموذج عرض تفاصيل العميل -->
            <div id="customer-details-modal" class="modal hidden">
                <div class="modal-content large">
                    <div class="modal-header">
                        <h3>تفاصيل العميل</h3>
                        <button id="close-details-modal" class="btn-close" onclick="closeModal('customer-details-modal')">&times;</button>
                    </div>
                    <div id="customer-details-content"></div>
                </div>
            </div>

            <!-- نموذج إضافة عميل بكامل البيانات -->
            <div id="detailed-customer-modal" class="modal hidden">
                <div class="modal-content extra-large">
                    <div class="modal-header">
                        <h3>إضافة عميل بكامل البيانات</h3>
                        <button id="close-detailed-customer-modal" class="btn-close" onclick="Customers.closeDetailedCustomerModal()">&times;</button>
                    </div>
                    <form id="detailed-customer-form" class="detailed-customer-form">
                        <!-- معلومات أساسية -->
                        <div class="form-section">
                            <h4><i class="fas fa-user"></i> المعلومات الأساسية</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-customer-code">كود العميل</label>
                                    <input type="text" id="detailed-customer-code" class="form-control">
                                    <button type="button" onclick="Customers.generateCustomerCode()" class="btn btn-sm btn-secondary">توليد تلقائي</button>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-name">اسم العميل *</label>
                                    <input type="text" id="detailed-customer-name" class="form-control" required>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-name-en">الاسم بالإنجليزية</label>
                                    <input type="text" id="detailed-customer-name-en" class="form-control">
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-customer-type">نوع العميل</label>
                                    <select id="detailed-customer-type" class="form-control">
                                        <option value="individual">فرد</option>
                                        <option value="company">شركة</option>
                                        <option value="government">جهة حكومية</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-category">فئة العميل</label>
                                    <select id="detailed-customer-category" class="form-control">
                                        <option value="regular">عادي</option>
                                        <option value="vip">مميز</option>
                                        <option value="wholesale">جملة</option>
                                        <option value="retail">تجزئة</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-status">حالة العميل</label>
                                    <select id="detailed-customer-status" class="form-control">
                                        <option value="active">نشط</option>
                                        <option value="inactive">غير نشط</option>
                                        <option value="blocked">محظور</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الاتصال -->
                        <div class="form-section">
                            <h4><i class="fas fa-phone"></i> معلومات الاتصال</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-customer-phone">رقم الهاتف الأساسي</label>
                                    <input type="tel" id="detailed-customer-phone" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-phone2">رقم هاتف إضافي</label>
                                    <input type="tel" id="detailed-customer-phone2" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-whatsapp">رقم الواتساب</label>
                                    <input type="tel" id="detailed-customer-whatsapp" class="form-control">
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-customer-email">البريد الإلكتروني</label>
                                    <input type="email" id="detailed-customer-email" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-website">الموقع الإلكتروني</label>
                                    <input type="url" id="detailed-customer-website" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-fax">رقم الفاكس</label>
                                    <input type="tel" id="detailed-customer-fax" class="form-control">
                                </div>
                            </div>
                        </div>

                        <!-- العنوان -->
                        <div class="form-section">
                            <h4><i class="fas fa-map-marker-alt"></i> العنوان</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-customer-country">الدولة</label>
                                    <input type="text" id="detailed-customer-country" class="form-control" value="المملكة العربية السعودية">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-city">المدينة</label>
                                    <input type="text" id="detailed-customer-city" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-district">الحي</label>
                                    <input type="text" id="detailed-customer-district" class="form-control">
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-customer-street">الشارع</label>
                                    <input type="text" id="detailed-customer-street" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-building">رقم المبنى</label>
                                    <input type="text" id="detailed-customer-building" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-postal">الرمز البريدي</label>
                                    <input type="text" id="detailed-customer-postal" class="form-control">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="detailed-customer-address">العنوان التفصيلي</label>
                                <textarea id="detailed-customer-address" class="form-control" rows="3"></textarea>
                            </div>
                        </div>

                        <!-- المعلومات المالية -->
                        <div class="form-section">
                            <h4><i class="fas fa-money-bill-wave"></i> المعلومات المالية</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-customer-balance">الرصيد الابتدائي</label>
                                    <input type="number" id="detailed-customer-balance" class="form-control" value="0" step="0.01">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-credit-limit">الحد الائتماني</label>
                                    <input type="number" id="detailed-customer-credit-limit" class="form-control" value="0" step="0.01">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-payment-terms">شروط الدفع (أيام)</label>
                                    <input type="number" id="detailed-customer-payment-terms" class="form-control" value="30" min="0">
                                </div>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-customer-discount">خصم افتراضي (%)</label>
                                    <input type="number" id="detailed-customer-discount" class="form-control" value="0" step="0.01" min="0" max="100">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-tax-number">الرقم الضريبي</label>
                                    <input type="text" id="detailed-customer-tax-number" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-currency">العملة المفضلة</label>
                                    <select id="detailed-customer-currency" class="form-control">
                                        <option value="EGP">جنيه مصري</option>
                                        <option value="SAR">ريال سعودي</option>
                                        <option value="USD">دولار أمريكي</option>
                                        <option value="EUR">يورو</option>
                                        <option value="AED">درهم إماراتي</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="form-section">
                            <h4><i class="fas fa-info-circle"></i> معلومات إضافية</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="detailed-customer-birthdate">تاريخ الميلاد</label>
                                    <input type="date" id="detailed-customer-birthdate" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-id-number">رقم الهوية/الإقامة</label>
                                    <input type="text" id="detailed-customer-id-number" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="detailed-customer-source">مصدر العميل</label>
                                    <select id="detailed-customer-source" class="form-control">
                                        <option value="">اختر المصدر</option>
                                        <option value="website">الموقع الإلكتروني</option>
                                        <option value="social">وسائل التواصل</option>
                                        <option value="referral">إحالة</option>
                                        <option value="advertising">إعلان</option>
                                        <option value="direct">مباشر</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="detailed-customer-notes">ملاحظات</label>
                                <textarea id="detailed-customer-notes" class="form-control" rows="3"></textarea>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ العميل
                            </button>
                            <button type="button" onclick="Customers.editDetailedCustomer()" class="btn btn-warning">
                                <i class="fas fa-edit"></i> تعديل العميل
                            </button>
                            <button type="button" onclick="Customers.clearDetailedCustomerForm()" class="btn btn-info">
                                <i class="fas fa-eraser"></i> مسح النموذج
                            </button>
                            <button type="button" onclick="Customers.saveAndAddAnotherCustomer()" class="btn btn-success">
                                <i class="fas fa-plus"></i> حفظ وإضافة آخر
                            </button>
                            <button type="button" onclick="App.goBack()" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        this.attachEvents();
        this.loadCustomers();
    },

    // إضافة الأحداث
    attachEvents() {
        // زر إضافة عميل جديد (سريع)
        document.getElementById('add-customer-btn').addEventListener('click', () => {
            this.showCustomerModal();
        });

        // زر إضافة عميل بكامل البيانات
        document.getElementById('add-detailed-customer-btn').addEventListener('click', () => {
            this.showDetailedCustomerModal();
        });

        // البحث
        document.getElementById('customer-search').addEventListener('input', (e) => {
            this.searchCustomers(e.target.value);
        });

        // نموذج العميل
        document.getElementById('customer-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCustomer();
        });

        // نموذج العميل المفصل
        document.getElementById('detailed-customer-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveDetailedCustomer();
        });

        // لا حاجة لمعالجات إضافية - يتم التعامل معها في النظام العام
    },

    // تحميل العملاء
    loadCustomers() {
        const data = DB.getData();
        const customers = data.customers || [];
        this.renderCustomers(customers);
    },

    // عرض العملاء في الجدول
    renderCustomers(customers) {
        const tbody = document.getElementById('customers-table-body');
        tbody.innerHTML = '';

        customers.forEach(customer => {
            const row = document.createElement('tr');
            const balanceClass = customer.balance > 0 ? 'positive' : customer.balance < 0 ? 'negative' : '';
            
            row.innerHTML = `
                <td>${customer.name}</td>
                <td>${customer.phone || '-'}</td>
                <td>${customer.email || '-'}</td>
                <td class="balance ${balanceClass}">${this.formatCurrency(customer.balance)}</td>
                <td class="actions">
                    <button onclick="Customers.viewCustomer(${customer.id})" class="btn btn-sm btn-info">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${!customer.isDefault ? `
                        <button onclick="Customers.editCustomer(${customer.id})" class="btn btn-sm btn-warning">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="Customers.deleteCustomer(${customer.id})" class="btn btn-sm btn-danger">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </td>
            `;
            tbody.appendChild(row);
        });
    },

    // البحث عن العملاء
    searchCustomers(query) {
        const customers = DB.search('customers', query, ['name', 'phone', 'email']);
        this.renderCustomers(customers);
    },

    // عرض نموذج العميل
    showCustomerModal(customer = null) {
        const modal = document.getElementById('customer-modal');
        const title = document.getElementById('modal-title');
        const form = document.getElementById('customer-form');

        if (customer) {
            title.textContent = 'تعديل العميل';
            document.getElementById('customer-name').value = customer.name;
            document.getElementById('customer-phone').value = customer.phone || '';
            document.getElementById('customer-email').value = customer.email || '';
            document.getElementById('customer-address').value = customer.address || '';
            document.getElementById('customer-balance').value = customer.balance || 0;
            form.dataset.customerId = customer.id;
        } else {
            title.textContent = 'إضافة عميل جديد';
            form.reset();
            delete form.dataset.customerId;
        }

        modal.classList.remove('hidden');
    },

    // إخفاء نموذج العميل
    hideCustomerModal() {
        document.getElementById('customer-modal').classList.add('hidden');
    },

    // حفظ العميل
    saveCustomer() {
        const form = document.getElementById('customer-form');
        const data = DB.getData();
        
        const customerData = {
            name: document.getElementById('customer-name').value.trim(),
            phone: document.getElementById('customer-phone').value.trim(),
            email: document.getElementById('customer-email').value.trim(),
            address: document.getElementById('customer-address').value.trim(),
            balance: parseFloat(document.getElementById('customer-balance').value) || 0
        };

        if (!customerData.name) {
            alert('يرجى إدخال اسم العميل');
            return;
        }

        if (form.dataset.customerId) {
            // تعديل عميل موجود
            const customerId = parseInt(form.dataset.customerId);
            const customerIndex = data.customers.findIndex(c => c.id === customerId);
            
            if (customerIndex !== -1) {
                data.customers[customerIndex] = {
                    ...data.customers[customerIndex],
                    ...customerData,
                    updatedAt: new Date().toISOString()
                };
            }
        } else {
            // إضافة عميل جديد
            const newCustomer = {
                id: DB.getNextId('customer'),
                ...customerData,
                isDefault: false,
                createdAt: new Date().toISOString()
            };
            data.customers.push(newCustomer);
        }

        DB.saveData(data);
        this.hideCustomerModal();
        this.loadCustomers();
        
        const message = form.dataset.customerId ? 'تم تعديل العميل بنجاح' : 'تم إضافة العميل بنجاح';
        this.showNotification(message, 'success');
    },

    // تعديل العميل
    editCustomer(id) {
        const data = DB.getData();
        const customer = data.customers.find(c => c.id === id);
        if (customer) {
            this.showCustomerModal(customer);
        }
    },

    // حذف العميل
    deleteCustomer(id) {
        if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
            const data = DB.getData();
            data.customers = data.customers.filter(c => c.id !== id);
            DB.saveData(data);
            this.loadCustomers();
            this.showNotification('تم حذف العميل بنجاح', 'success');
        }
    },

    // عرض تفاصيل العميل
    viewCustomer(id) {
        const data = DB.getData();
        const customer = data.customers.find(c => c.id === id);
        if (!customer) return;

        // جلب معاملات العميل
        const sales = data.sales.filter(s => s.customerId === id);
        const payments = data.payments.filter(p => p.customerId === id);

        const content = document.getElementById('customer-details-content');
        content.innerHTML = `
            <div class="customer-info">
                <h4>معلومات العميل</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <label>الاسم:</label>
                        <span>${customer.name}</span>
                    </div>
                    <div class="info-item">
                        <label>الهاتف:</label>
                        <span>${customer.phone || '-'}</span>
                    </div>
                    <div class="info-item">
                        <label>البريد:</label>
                        <span>${customer.email || '-'}</span>
                    </div>
                    <div class="info-item">
                        <label>العنوان:</label>
                        <span>${customer.address || '-'}</span>
                    </div>
                    <div class="info-item">
                        <label>الرصيد الحالي:</label>
                        <span class="balance ${customer.balance > 0 ? 'positive' : customer.balance < 0 ? 'negative' : ''}">${this.formatCurrency(customer.balance)}</span>
                    </div>
                </div>
            </div>

            <div class="customer-transactions">
                <h4>آخر المعاملات</h4>
                <div class="transactions-list">
                    ${sales.slice(-5).map(sale => `
                        <div class="transaction-item">
                            <span>فاتورة مبيعات #${sale.id}</span>
                            <span>${new Date(sale.date).toLocaleDateString('ar-SA')}</span>
                            <span class="amount">${this.formatCurrency(sale.total)}</span>
                        </div>
                    `).join('')}
                </div>
            </div>

            <div class="customer-actions">
                <button onclick="Sales.newSaleForCustomer(${id})" class="btn btn-primary">
                    <i class="fas fa-shopping-cart"></i> فاتورة جديدة
                </button>
                <button onclick="Customers.addPayment(${id})" class="btn btn-success">
                    <i class="fas fa-money-bill"></i> تسجيل دفعة
                </button>
                <button onclick="Reports.customerStatement(${id})" class="btn btn-info">
                    <i class="fas fa-file-alt"></i> كشف حساب
                </button>
            </div>
        `;

        document.getElementById('customer-details-modal').classList.remove('hidden');
    },

    // إخفاء نموذج التفاصيل
    hideDetailsModal() {
        document.getElementById('customer-details-modal').classList.add('hidden');
    },

    // تنسيق العملة
    formatCurrency(amount) {
        return App.formatCurrency(amount);
    },

    // عرض نموذج العميل المفصل
    showDetailedCustomerModal() {
        const modal = document.getElementById('detailed-customer-modal');
        this.clearDetailedCustomerForm();
        modal.classList.remove('hidden');
    },

    // حفظ العميل المفصل
    saveDetailedCustomer() {
        const data = DB.getData();

        // جمع البيانات من النموذج
        const customerData = {
            id: DB.getNextId('customer'),
            code: document.getElementById('detailed-customer-code').value.trim(),
            name: document.getElementById('detailed-customer-name').value.trim(),
            nameEn: document.getElementById('detailed-customer-name-en').value.trim(),
            type: document.getElementById('detailed-customer-type').value,
            category: document.getElementById('detailed-customer-category').value,
            status: document.getElementById('detailed-customer-status').value,
            phone: document.getElementById('detailed-customer-phone').value.trim(),
            phone2: document.getElementById('detailed-customer-phone2').value.trim(),
            whatsapp: document.getElementById('detailed-customer-whatsapp').value.trim(),
            email: document.getElementById('detailed-customer-email').value.trim(),
            website: document.getElementById('detailed-customer-website').value.trim(),
            fax: document.getElementById('detailed-customer-fax').value.trim(),
            country: document.getElementById('detailed-customer-country').value.trim(),
            city: document.getElementById('detailed-customer-city').value.trim(),
            district: document.getElementById('detailed-customer-district').value.trim(),
            street: document.getElementById('detailed-customer-street').value.trim(),
            building: document.getElementById('detailed-customer-building').value.trim(),
            postal: document.getElementById('detailed-customer-postal').value.trim(),
            address: document.getElementById('detailed-customer-address').value.trim(),
            balance: parseFloat(document.getElementById('detailed-customer-balance').value) || 0,
            creditLimit: parseFloat(document.getElementById('detailed-customer-credit-limit').value) || 0,
            paymentTerms: parseInt(document.getElementById('detailed-customer-payment-terms').value) || 30,
            discount: parseFloat(document.getElementById('detailed-customer-discount').value) || 0,
            taxNumber: document.getElementById('detailed-customer-tax-number').value.trim(),
            currency: document.getElementById('detailed-customer-currency').value,
            birthdate: document.getElementById('detailed-customer-birthdate').value,
            idNumber: document.getElementById('detailed-customer-id-number').value.trim(),
            source: document.getElementById('detailed-customer-source').value,
            notes: document.getElementById('detailed-customer-notes').value.trim(),
            isDefault: false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // التحقق من البيانات المطلوبة
        if (!customerData.name) {
            alert('يرجى إدخال اسم العميل');
            document.getElementById('detailed-customer-name').focus();
            return;
        }

        // التحقق من عدم تكرار الكود إذا تم إدخاله
        if (customerData.code) {
            const existingCustomer = data.customers.find(c => c.code === customerData.code);
            if (existingCustomer) {
                alert('كود العميل موجود مسبقاً، يرجى استخدام كود آخر');
                document.getElementById('detailed-customer-code').focus();
                return;
            }
        }

        // إضافة العميل
        data.customers.push(customerData);
        DB.saveData(data);

        alert('تم حفظ العميل بنجاح');
        this.loadCustomers();
        this.closeDetailedCustomerModal();
    },

    // حفظ وإضافة عميل آخر
    saveAndAddAnotherCustomer() {
        this.saveDetailedCustomer();
        setTimeout(() => {
            this.clearDetailedCustomerForm();
            document.getElementById('detailed-customer-name').focus();
        }, 100);
    },

    // مسح النموذج المفصل
    clearDetailedCustomerForm() {
        const form = document.getElementById('detailed-customer-form');
        form.reset();

        // تعيين القيم الافتراضية
        document.getElementById('detailed-customer-type').value = 'individual';
        document.getElementById('detailed-customer-category').value = 'regular';
        document.getElementById('detailed-customer-status').value = 'active';
        document.getElementById('detailed-customer-country').value = 'جمهورية مصر العربية';
        document.getElementById('detailed-customer-balance').value = 0;
        document.getElementById('detailed-customer-credit-limit').value = 0;
        document.getElementById('detailed-customer-payment-terms').value = 30;
        document.getElementById('detailed-customer-discount').value = 0;
        document.getElementById('detailed-customer-currency').value = 'EGP';
    },

    // توليد كود العميل تلقائياً
    generateCustomerCode() {
        const data = DB.getData();
        const nextId = data.lastIds.customer + 1;
        const code = `CUS${nextId.toString().padStart(6, '0')}`;
        document.getElementById('detailed-customer-code').value = code;
    },

    // إغلاق نموذج العميل المفصل
    closeDetailedCustomerModal() {
        document.getElementById('detailed-customer-modal').classList.add('hidden');
    },

    // عرض الإشعارات
    showNotification(message, type = 'info') {
        // يمكن تطوير نظام إشعارات أكثر تقدماً لاحقاً
        alert(message);
    }
};
